# Guide d'utilisation du système d'authentification Firebase

## Vue d'ensemble

Le système d'authentification a été mis à jour pour utiliser Firebase et créer des utilisateurs dans des collections séparées selon leur type (commercial ou merchandiser). Tous les nouveaux utilisateurs sont créés avec un statut "inactif" par défaut et doivent être activés par un administrateur.

## Collections Firebase

### 1. Collection `users` (collection principale)
Contient les informations de base de tous les utilisateurs :
```
users/{uid}/
├── email: string
├── nomComplet: string
├── userType: string ("commercial" ou "merchandiser")
├── statut: string ("inactif" ou "actif")
├── createdAt: timestamp
└── lastLogin: timestamp
```

### 2. Collection `commercial`
Contient les informations détaillées des utilisateurs commerciaux :
```
commercial/{uid}/
├── userId: string
├── email: string
├── nom: string
├── telephone: string
├── mobile: string
├── territoire: string
├── status: string ("inactif" ou "actif")
├── createdAt: timestamp
└── updatedAt: timestamp
```

### 3. Collection `merchandiser`
Contient les informations détaillées des merchandisers :
```
merchandiser/{uid}/
├── userId: string
├── email: string
├── nom: string
├── telephone: string
├── mobile: string
├── zone: string (territoire)
├── status: string ("inactif" ou "actif")
├── actif: boolean
├── createdAt: timestamp
└── updatedAt: timestamp
```

## Processus d'inscription

1. **Remplir le formulaire** : L'utilisateur choisit son type (commercial/merchandiser) et remplit ses informations
2. **Création du compte** : Le système crée automatiquement :
   - Un compte Firebase Authentication
   - Un document dans la collection `users`
   - Un document dans la collection spécifique (`commercial` ou `merchandiser`)
3. **Statut par défaut** : Le statut est automatiquement défini sur "inactif"
4. **Redirection** : L'utilisateur est redirigé vers l'écran de connexion avec un message d'information

## Processus de connexion

1. **Tentative de connexion** : L'utilisateur entre ses identifiants
2. **Vérification Firebase Auth** : Validation email/mot de passe
3. **Vérification du statut** : Le système vérifie si le compte est actif
4. **Accès accordé/refusé** : 
   - Si actif : connexion réussie
   - Si inactif : message d'erreur et déconnexion automatique

## Administration des utilisateurs

### Accès à l'interface admin
1. Sur l'écran de connexion, tapez 5 fois sur le logo VitaBrosse
2. L'écran d'administration s'ouvre automatiquement

### Fonctionnalités admin
- **Voir tous les utilisateurs en attente** : Liste des comptes inactifs
- **Activer un utilisateur** : Changer le statut de "inactif" à "actif"
- **Supprimer un utilisateur** : Supprimer complètement le compte (avec confirmation)
- **Rafraîchir la liste** : Mettre à jour la liste des utilisateurs

## Services utilisés

### AuthProvider
- Interface principale pour l'authentification
- Utilise FirebaseAuthProvider en arrière-plan
- Méthodes : `login()`, `signup()`, `logout()`

### FirebaseAuthProvider
- Gestion directe avec Firebase
- Création des profils utilisateur
- Vérification des statuts

### AdminService
- Gestion administrative des utilisateurs
- Activation/désactivation des comptes
- Récupération des listes d'utilisateurs

## Utilisation en développement

### Tester l'inscription
1. Lancez l'application
2. Allez sur "S'inscrire"
3. Choisissez le type d'utilisateur
4. Remplissez le formulaire
5. Vérifiez que l'utilisateur apparaît dans l'interface admin (statut inactif)

### Tester l'activation
1. Accédez à l'interface admin (5 taps sur le logo)
2. Trouvez l'utilisateur dans la liste
3. Cliquez sur "Activer"
4. Tentez de vous connecter avec ce compte

### Tester la connexion
1. Essayez de vous connecter avec un compte inactif → Erreur attendue
2. Activez le compte via l'interface admin
3. Essayez de vous connecter à nouveau → Succès attendu

## Structure des données

Les données sont dupliquées entre la collection `users` et les collections spécifiques pour :
- **Performance** : Requêtes plus rapides sur les collections spécialisées
- **Flexibilité** : Possibilité d'ajouter des champs spécifiques à chaque type
- **Compatibilité** : Maintien de la compatibilité avec l'ancien système

## Sécurité

- Les mots de passe sont gérés par Firebase Authentication
- Les règles Firestore doivent être configurées pour limiter l'accès
- L'interface admin est accessible uniquement via un geste secret
- Les utilisateurs inactifs ne peuvent pas se connecter

## Maintenance

### Surveillance des nouveaux utilisateurs
- Vérifiez régulièrement l'interface admin pour les nouveaux comptes
- Activez les comptes légitimes rapidement
- Supprimez les comptes suspects

### Sauvegarde
- Firebase gère automatiquement la persistance
- Configurez des sauvegardes régulières via la console Firebase

### Logs
- Les erreurs sont affichées dans la console Flutter
- Utilisez `print()` statements pour le débogage (marqués avec 🔥)
