import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/firebase_auth_provider.dart';
import 'providers/auth_provider.dart';

class AuthProviderTest extends StatefulWidget {
  @override
  _AuthProviderTestState createState() => _AuthProviderTestState();
}

class _AuthProviderTestState extends State<AuthProviderTest> {
  String _status = 'Prêt à tester l\'authentification via Provider';
  bool _isLoading = false;

  late AuthProvider _authProvider;
  late FirebaseAuthProvider _firebaseAuthProvider;

  @override
  void initState() {
    super.initState();
    _authProvider = Provider.of<AuthProvider>(context, listen: false);
    _firebaseAuthProvider = Provider.of<FirebaseAuthProvider>(
      context,
      listen: false,
    );
  }

  Future<void> _testAuthProvider() async {
    setState(() {
      _isLoading = true;
      _status = 'Test en cours...';
    });

    try {
      print('🔬 Début des tests du provider d\'authentification');

      // Test 1: Test d'inscription via AuthProvider
      await _testSignUpWithProvider();

      // Test 2: Test de connexion via AuthProvider
      await _testSignInWithProvider();

      setState(() {
        _status =
            '✅ Tous les tests du provider réussis!\n\n• Inscription via Provider ✓\n• Connexion via Provider ✓\n• Gestion des erreurs ✓\n\nProvider d\'authentification opérationnel!';
        _isLoading = false;
      });
    } catch (e) {
      print('❌ Erreur lors du test du provider: $e');
      setState(() {
        _status = '❌ Erreur dans le provider: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testSignUpWithProvider() async {
    try {
      print('📝 Test d\'inscription avec AuthProvider...');

      final testEmail =
          'providertest${DateTime.now().millisecondsSinceEpoch}@example.com';

      print('📝 Inscription avec email: $testEmail');

      final result = await _authProvider.signup(
        nomComplet: 'Provider Test User',
        email: testEmail,
        password: 'providertest123',
        telephone: '**********',
        mobile: '**********',
        territoire: 'Provider Test Territory',
        userType: 'commercial',
      );

      if (result) {
        print('✅ Inscription via AuthProvider réussie');

        // Déconnecter pour le test suivant
        await _authProvider.logout();
        print('✅ Déconnexion après inscription réussie');
      } else {
        print('❌ Échec de l\'inscription via AuthProvider');
        if (_authProvider.error != null) {
          print('Erreur: ${_authProvider.error}');
        }
        if (_firebaseAuthProvider.errorMessage != null) {
          print('Erreur Firebase: ${_firebaseAuthProvider.errorMessage}');
        }
      }
    } catch (e) {
      print('❌ Exception lors du test d\'inscription: $e');
      throw e;
    }
  }

  Future<void> _testSignInWithProvider() async {
    try {
      print('🔑 Test de connexion avec AuthProvider...');

      // Utiliser un compte qui devrait exister (créé précédemment)
      // Ou créer un compte de test spécifique
      final testEmail = '<EMAIL>';
      final testPassword = 'test123456';

      print('🔑 Tentative de connexion avec: $testEmail');

      final result = await _authProvider.login(testEmail, testPassword);

      if (result) {
        print('✅ Connexion via AuthProvider réussie');

        // Vérifier l'état de l'utilisateur
        if (_authProvider.isAuthenticated) {
          print('✅ État d\'authentification confirmé');
        } else {
          print('❌ État d\'authentification incohérent');
        }

        // Déconnecter
        await _authProvider.logout();
        print('✅ Déconnexion après test réussie');
      } else {
        print('⚠️ Connexion échouée (possiblement attendu si compte inactif)');
        if (_authProvider.error != null) {
          print('Message d\'erreur: ${_authProvider.error}');
        }
      }
    } catch (e) {
      print('❌ Exception lors du test de connexion: $e');
      throw e;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Test Authentication Provider'),
        backgroundColor: Colors.purple,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _status.startsWith('✅')
                  ? Icons.check_circle
                  : _status.startsWith('❌')
                  ? Icons.error
                  : Icons.science,
              size: 64,
              color:
                  _status.startsWith('✅')
                      ? Colors.green
                      : _status.startsWith('❌')
                      ? Colors.red
                      : Colors.purple,
            ),
            SizedBox(height: 20),
            Text(
              _status,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            SizedBox(height: 40),
            ElevatedButton(
              onPressed: _isLoading ? null : _testAuthProvider,
              style: ElevatedButton.styleFrom(backgroundColor: Colors.purple),
              child:
                  _isLoading
                      ? CircularProgressIndicator(color: Colors.white)
                      : Text(
                        'Tester AuthProvider',
                        style: TextStyle(color: Colors.white),
                      ),
            ),
            SizedBox(height: 20),
            Consumer<FirebaseAuthProvider>(
              builder: (context, firebaseAuth, child) {
                return Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'État Firebase Auth Provider:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        SizedBox(height: 8),
                        Text('Loading: ${firebaseAuth.isLoading}'),
                        Text(
                          'User: ${firebaseAuth.user?.email ?? 'Non connecté'}',
                        ),
                        Text('Error: ${firebaseAuth.errorMessage ?? 'Aucune'}'),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
