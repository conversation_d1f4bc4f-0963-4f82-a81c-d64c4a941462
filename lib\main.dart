import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:provider/provider.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';
import 'providers/firebase_client_provider.dart';
import 'providers/produit_provider.dart';
import 'providers/commande_provider.dart';
import 'providers/merchandiser_provider.dart';
import 'providers/magasin_provider.dart';
import 'providers/parcours_provider.dart';
import 'providers/catalogue_provider.dart';
import 'providers/tache_merchandising_provider.dart';
import 'providers/mission_provider.dart';
import 'providers/rapport_provider.dart';
import 'models/devis.dart';
import 'services/devis_service.dart';
import 'screens/auth/login_screen.dart';
import 'providers/auth_provider.dart';
import 'providers/firebase_auth_provider.dart';
import 'utils/catalogue_initializer.dart';

// Provider pour les devis
class DevisProvider with ChangeNotifier {
  final DevisService _devisService = DevisService();

  List<Devis> _devis = [];
  bool _isLoading = false;
  String? _error;
  List<Devis> get devis => _devis;
  bool get isLoading => _isLoading;
  String? get error => _error;

  Future<void> chargerDevis() async {
    _isLoading = true;
    notifyListeners();
    try {
      _devis = await _devisService.obtenirTousLesDevis();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des devis: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> rechercherDevis(String terme) async {
    _isLoading = true;
    notifyListeners();
    try {
      if (terme.isEmpty) {
        _devis = await _devisService.obtenirTousLesDevis();
      } else {
        _devis =
            _devis
                .where(
                  (devis) =>
                      devis.numero.toLowerCase().contains(
                        terme.toLowerCase(),
                      ) ||
                      devis.clientId.toLowerCase().contains(
                        terme.toLowerCase(),
                      ),
                )
                .toList();
      }
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<String> genererNumeroDevis() async {
    return await _devisService.genererNumeroDevis();
  }

  Future<bool> creerDevis(Devis devis) async {
    try {
      await _devisService.creerDevis(devis);
      await chargerDevis();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la création du devis: $e';
      notifyListeners();
      return false;
    }
  }

  Future<bool> mettreAJourStatut(String devisId, String nouveauStatut) async {
    try {
      await _devisService.mettreAJourStatut(devisId, nouveauStatut);
      await chargerDevis();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la mise à jour du statut: $e';
      notifyListeners();
      return false;
    }
  }

  Future<bool> supprimerDevis(String devisId) async {
    try {
      await _devisService.supprimerDevis(devisId);
      await chargerDevis();
      return true;
    } catch (e) {
      _error = 'Erreur lors de la suppression: $e';
      notifyListeners();
      return false;
    }
  }

  Future<String?> dupliquerDevis(String devisId) async {
    try {
      return await _devisService.dupliquerDevis(devisId);
    } catch (e) {
      _error = 'Erreur lors de la duplication: $e';
      notifyListeners();
      return null;
    }
  }

  Future<String?> transformerEnCommande(String devisId) async {
    try {
      return await _devisService.transformerEnCommande(devisId);
    } catch (e) {
      _error = 'Erreur lors de la transformation en commande: $e';
      notifyListeners();
      return null;
    }
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );

    await FirebaseService.initializeFirestore();

    runApp(MyApp());

    // Initialiser les catalogues de démonstration en arrière-plan après le lancement
    _initializeCataloguesInBackground();
  } catch (e) {
    print('Erreur lors de l\'initialisation Firebase: $e');
    // En cas d'erreur, lancer l'app avec une interface d'erreur
    runApp(MyErrorApp(error: e.toString()));
  }
}

/// Initialise les catalogues en arrière-plan pour ne pas ralentir le démarrage
void _initializeCataloguesInBackground() async {
  try {
    await CatalogueInitializer.initializeSampleCatalogues();
    print('Catalogues de démonstration initialisés en arrière-plan');
  } catch (e) {
    print(
      'Erreur lors de l\'initialisation des catalogues en arrière-plan: $e',
    );
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => AuthProvider()),
        ChangeNotifierProvider(create: (context) => FirebaseAuthProvider()),
        ChangeNotifierProvider(create: (context) => FirebaseClientProvider()),
        ChangeNotifierProvider(create: (context) => ProduitProvider()),
        ChangeNotifierProvider(create: (context) => CommandeProvider()),
        ChangeNotifierProvider(create: (context) => DevisProvider()),
        ChangeNotifierProvider(create: (context) => MerchandiserProvider()),
        ChangeNotifierProvider(create: (context) => MagasinProvider()),
        ChangeNotifierProvider(create: (context) => ParcoursProvider()),
        ChangeNotifierProvider(create: (context) => CatalogueProvider()),
        ChangeNotifierProvider(
          create: (context) => TacheMerchandisingProvider(),
        ),
        ChangeNotifierProvider(create: (context) => MissionProvider()),
        ChangeNotifierProvider(create: (context) => RapportProvider()),
      ],
      child: MaterialApp(
        title: 'VitaBrosse Pro',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
        home: const LoginScreen(),
      ),
    );
  }
}

class MyErrorApp extends StatelessWidget {
  final String error;

  const MyErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'VitaBrosse Pro - Erreur',
      debugShowCheckedModeBanner: false,
      theme: ThemeData(primarySwatch: Colors.red, useMaterial3: true),
      home: Scaffold(
        appBar: AppBar(
          title: Text('Erreur d\'initialisation'),
          backgroundColor: Colors.red,
          foregroundColor: Colors.white,
        ),
        body: Center(
          child: Padding(
            padding: EdgeInsets.all(20),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 100, color: Colors.red),
                SizedBox(height: 20),
                Text(
                  'VitaBrosse Pro',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 20),
                Text(
                  'Erreur lors de l\'initialisation Firebase:',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 10),
                Text(
                  error,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14, color: Colors.red),
                ),
                SizedBox(height: 30),
                ElevatedButton(
                  onPressed: () {
                    // Relancer l'application
                    main();
                  },
                  child: Text('Réessayer'),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
