import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/client.dart';
import '../services/firebase_service.dart';

class FirebaseClientProvider extends ChangeNotifier {
  List<Client> _clients = [];
  bool _isLoading = false;
  String? _errorMessage;

  // Getters
  List<Client> get clients => _clients;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  // Gérer les erreurs
  String? _error;
  String? get error => _error;

  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// Charger tous les clients
  Future<void> loadClients() async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final QuerySnapshot snapshot =
          await FirebaseService.clients
              .orderBy('createdAt', descending: true)
              .get();

      _clients =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Client.fromFirestore(doc.id, data);
          }).toList();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Erreur lors du chargement des clients';
      notifyListeners();
      print('Erreur loadClients: $e');
    }
  }

  /// Écouter les changements en temps réel
  Stream<List<Client>> getClientsStream() {
    return FirebaseService.clients
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
          return snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Client.fromFirestore(doc.id, data);
          }).toList();
        });
  }

  /// Ajouter un client
  Future<bool> addClient(Client client) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      final docRef = await FirebaseService.clients.add(client.toFirestore());

      // Ajouter à la liste locale
      final newClient = client.copyWith(id: docRef.id);
      _clients.insert(0, newClient);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Erreur lors de l\'ajout du client';
      notifyListeners();
      print('Erreur addClient: $e');
      return false;
    }
  }

  /// Ajouter un nouveau client
  Future<bool> ajouterClient(Client client) async {
    try {
      _isLoading = true;
      notifyListeners();

      final docRef = await FirebaseService.clients.add(client.toFirestore());

      // Ajouter le client à la liste locale avec l'ID généré
      final nouveauClient = client.copyWith(id: docRef.id);
      _clients.add(nouveauClient);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de l\'ajout du client: $e');
      return false;
    }
  }

  /// Modifier un client
  Future<bool> updateClient(Client client) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await FirebaseService.clients.doc(client.id).update(client.toFirestore());

      // Mettre à jour la liste locale
      final index = _clients.indexWhere((c) => c.id == client.id);
      if (index != -1) {
        _clients[index] = client;
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Erreur lors de la modification du client';
      notifyListeners();
      print('Erreur updateClient: $e');
      return false;
    }
  }

  /// Modifier un client existant
  Future<bool> modifierClient(Client client) async {
    try {
      _isLoading = true;
      notifyListeners();

      if (client.id == null) {
        throw Exception('ID du client requis pour la modification');
      }

      await FirebaseService.clients.doc(client.id).update(client.toFirestore());

      // Mettre à jour la liste locale
      final index = _clients.indexWhere((c) => c.id == client.id);
      if (index != -1) {
        _clients[index] = client;
      }

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la modification du client: $e');
      return false;
    }
  }

  /// Supprimer un client
  Future<bool> deleteClient(String clientId) async {
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();

      await FirebaseService.clients.doc(clientId).delete();

      // Supprimer de la liste locale
      _clients.removeWhere((c) => c.id == clientId);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _isLoading = false;
      _errorMessage = 'Erreur lors de la suppression du client';
      notifyListeners();
      print('Erreur deleteClient: $e');
      return false;
    }
  }

  /// Supprimer un client
  Future<bool> supprimerClient(String id) async {
    try {
      _isLoading = true;
      notifyListeners();

      await FirebaseService.clients.doc(id).delete();

      // Supprimer de la liste locale
      _clients.removeWhere((c) => c.id == id);

      _isLoading = false;
      notifyListeners();
      return true;
    } catch (e) {
      _setError('Erreur lors de la suppression du client: $e');
      return false;
    }
  }

  /// Obtenir un client par ID
  Future<Client?> getClientById(String clientId) async {
    try {
      final doc = await FirebaseService.clients.doc(clientId).get();
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return Client.fromFirestore(doc.id, data);
      }
      return null;
    } catch (e) {
      print('Erreur getClientById: $e');
      return null;
    }
  }

  /// Rechercher des clients
  Future<void> searchClients(String query) async {
    if (query.isEmpty) {
      await loadClients();
      return;
    }

    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final snapshot = await FirebaseService.clients.get();
      final allClients =
          snapshot.docs.map((doc) {
            final data = doc.data() as Map<String, dynamic>;
            return Client.fromFirestore(doc.id, data);
          }).toList();

      final lowerQuery = query.toLowerCase();
      _clients =
          allClients.where((client) {
            return (client.nom?.toLowerCase().contains(lowerQuery) ?? false) ||
                (client.prenom?.toLowerCase().contains(lowerQuery) ?? false) ||
                (client.nomClient?.toLowerCase().contains(lowerQuery) ??
                    false) ||
                client.email.toLowerCase().contains(lowerQuery) ||
                (client.telephone?.contains(query) ?? false) ||
                (client.tel?.contains(query) ?? false);
          }).toList();
    } catch (e) {
      _setError('Erreur lors de la recherche: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Charger tous les clients (alias pour loadClients)
  Future<void> chargerClients() async {
    return loadClients();
  }

  /// Rechercher des clients (alias pour searchClients)
  Future<void> rechercherClients(String query) async {
    return searchClients(query);
  }

  /// Obtenir un client par son ID
  Client? obtenirClientParId(String? id) {
    if (id == null) return null;
    try {
      return _clients.firstWhere((client) => client.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Vérifier si les clients sont chargés
  bool get isLoaded => _clients.isNotEmpty;

  /// Obtenir les statistiques des clients
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      final totalClients = _clients.length;
      final now = DateTime.now();
      final oneWeekAgo = now.subtract(const Duration(days: 7));

      final nouveauxClients =
          _clients
              .where((client) => client.primaryCreationDate.isAfter(oneWeekAgo))
              .length;

      return {
        'totalClients': totalClients,
        'nouveauxClients': nouveauxClients,
        'clientsActifs':
            totalClients, // Tous les clients sont considérés actifs
        'tauxCroissance':
            nouveauxClients > 0
                ? (nouveauxClients / totalClients * 100).toStringAsFixed(1)
                : '0.0',
      };
    } catch (e) {
      return {
        'totalClients': 0,
        'nouveauxClients': 0,
        'clientsActifs': 0,
        'tauxCroissance': '0.0',
      };
    }
  }

  /// Effacer le message d'erreur
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
