import 'package:flutter/foundation.dart';
import '../models/tache_merchandising.dart';
import '../services/tache_merchandising_service.dart';

class TacheMerchandisingProvider extends ChangeNotifier {
  final TacheMerchandisingService _tacheService = TacheMerchandisingService();

  List<TacheMerchandising> _taches = [];
  bool _isLoading = false;
  String? _error;
  DateTime _selectedDate = DateTime.now();
  TypeTache? _selectedType;
  StatutTache? _selectedStatut;
  PrioriteTache? _selectedPriorite;

  List<TacheMerchandising> get taches => _taches;
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime get selectedDate => _selectedDate;
  TypeTache? get selectedType => _selectedType;
  StatutTache? get selectedStatut => _selectedStatut;
  PrioriteTache? get selectedPriorite => _selectedPriorite;

  // Charger toutes les tâches
  Future<void> chargerTaches() async {
    _setLoading(true);
    try {
      _taches = await _tacheService.obtenirToutesLesTaches();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des tâches: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Ajouter une tâche
  Future<bool> ajouterTache(TacheMerchandising tache) async {
    try {
      final id = await _tacheService.ajouterTache(tache);
      if (id > 0) {
        _taches.add(tache.copyWith(id: id));
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de l\'ajout de la tâche: $e';
      notifyListeners();
      return false;
    }
  }

  // Modifier une tâche
  Future<bool> modifierTache(TacheMerchandising tache) async {
    try {
      final success = await _tacheService.modifierTache(tache);
      if (success) {
        final index = _taches.indexWhere((t) => t.id == tache.id);
        if (index != -1) {
          _taches[index] = tache;
          notifyListeners();
        }
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la modification de la tâche: $e';
      notifyListeners();
      return false;
    }
  }

  // Supprimer une tâche
  Future<bool> supprimerTache(int id) async {
    try {
      final success = await _tacheService.supprimerTache(id);
      if (success) {
        _taches.removeWhere((t) => t.id == id);
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la suppression de la tâche: $e';
      notifyListeners();
      return false;
    }
  }

  // Rechercher des tâches
  Future<void> rechercherTaches(String terme) async {
    _setLoading(true);
    try {
      _taches = await _tacheService.rechercherTaches(terme);
      _error = null;
    } catch (e) {
      _error = 'Erreur lors de la recherche: $e';
    } finally {
      _setLoading(false);
    }
  }

  // Obtenir les statistiques
  Future<Map<String, dynamic>> obtenirStatistiques() async {
    try {
      return await _tacheService.obtenirStatistiques();
    } catch (e) {
      return {
        'nombreTotal': 0,
        'planifiees': 0,
        'enCours': 0,
        'terminees': 0,
        'enRetard': 0,
      };
    }
  }

  // Obtenir une tâche par ID
  TacheMerchandising? obtenirTacheParId(int id) {
    try {
      return _taches.firstWhere((t) => t.id == id);
    } catch (e) {
      return null;
    }
  }

  // Obtenir les tâches par merchandiser
  List<TacheMerchandising> obtenirTachesParMerchandiser(int merchandiserId) {
    return _taches.where((t) => t.merchandiserId == merchandiserId).toList();
  }

  // Obtenir les tâches par date
  List<TacheMerchandising> obtenirTachesParDate(DateTime date) {
    return _taches
        .where(
          (t) =>
              t.dateEcheance.year == date.year &&
              t.dateEcheance.month == date.month &&
              t.dateEcheance.day == date.day,
        )
        .toList();
  }

  // Obtenir les tâches par statut
  List<TacheMerchandising> obtenirTachesParStatut(StatutTache statut) {
    return _taches.where((t) => t.statut == statut).toList();
  }

  // Obtenir les tâches par type
  List<TacheMerchandising> obtenirTachesParType(TypeTache type) {
    return _taches.where((t) => t.type == type).toList();
  }

  // Obtenir les tâches par priorité
  List<TacheMerchandising> obtenirTachesParPriorite(PrioriteTache priorite) {
    return _taches.where((t) => t.priorite == priorite).toList();
  }

  // Obtenir les tâches d'aujourd'hui
  List<TacheMerchandising> get tachesAujourdhui {
    final aujourdhui = DateTime.now();
    return obtenirTachesParDate(aujourdhui);
  }

  // Obtenir les tâches en retard
  List<TacheMerchandising> get tachesEnRetard {
    final maintenant = DateTime.now();
    return _taches
        .where(
          (t) =>
              t.dateEcheance.isBefore(maintenant) &&
              t.statut != StatutTache.terminee,
        )
        .toList();
  }

  // Obtenir les tâches avec rappel
  List<TacheMerchandising> get tachesAvecRappel {
    final maintenant = DateTime.now();
    return _taches
        .where(
          (t) =>
              t.rappel &&
              t.dateRappel != null &&
              t.dateRappel!.isBefore(maintenant) &&
              t.statut != StatutTache.terminee,
        )
        .toList();
  }

  // Obtenir les tâches de la semaine
  List<TacheMerchandising> get tachesSemaine {
    final maintenant = DateTime.now();
    final debutSemaine = maintenant.subtract(
      Duration(days: maintenant.weekday - 1),
    );
    final finSemaine = debutSemaine.add(const Duration(days: 6));
    return _taches
        .where(
          (t) =>
              t.dateEcheance.isAfter(debutSemaine) &&
              t.dateEcheance.isBefore(finSemaine.add(const Duration(days: 1))),
        )
        .toList();
  }

  // Obtenir les tâches du mois
  List<TacheMerchandising> obtenirTachesMois([DateTime? mois]) {
    final dateMois = mois ?? DateTime.now();
    return _taches
        .where(
          (t) =>
              t.dateEcheance.year == dateMois.year &&
              t.dateEcheance.month == dateMois.month,
        )
        .toList();
  }

  // Démarrer une tâche
  Future<bool> demarrerTache(int id) async {
    try {
      final tache = obtenirTacheParId(id);
      if (tache != null && tache.statut == StatutTache.planifiee) {
        final tacheModifiee = tache.copyWith(
          statut: StatutTache.en_cours,
          heureDebut: DateTime.now(),
        );
        return await modifierTache(tacheModifiee);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors du démarrage de la tâche: $e';
      notifyListeners();
      return false;
    }
  }

  // Terminer une tâche
  Future<bool> terminerTache(int id, {String? commentaires}) async {
    try {
      final tache = obtenirTacheParId(id);
      if (tache != null && tache.statut == StatutTache.en_cours) {
        final tacheModifiee = tache.copyWith(
          statut: StatutTache.terminee,
          heureFin: DateTime.now(),
          dateRealisation: DateTime.now(),
          commentaires: commentaires ?? tache.commentaires,
        );
        return await modifierTache(tacheModifiee);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de la finalisation de la tâche: $e';
      notifyListeners();
      return false;
    }
  }

  // Reporter une tâche
  Future<bool> reporterTache(
    int id,
    DateTime nouvelleDate, {
    String? commentaires,
  }) async {
    try {
      final tache = obtenirTacheParId(id);
      if (tache != null) {
        final tacheModifiee = tache.copyWith(
          statut: StatutTache.reportee,
          dateEcheance: nouvelleDate,
          commentaires: commentaires ?? tache.commentaires,
        );
        return await modifierTache(tacheModifiee);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors du report de la tâche: $e';
      notifyListeners();
      return false;
    }
  }

  // Annuler une tâche
  Future<bool> annulerTache(int id, {String? commentaires}) async {
    try {
      final tache = obtenirTacheParId(id);
      if (tache != null) {
        final tacheModifiee = tache.copyWith(
          statut: StatutTache.annulee,
          commentaires: commentaires ?? tache.commentaires,
        );
        return await modifierTache(tacheModifiee);
      }
      return false;
    } catch (e) {
      _error = 'Erreur lors de l\'annulation de la tâche: $e';
      notifyListeners();
      return false;
    }
  }

  // Changer la date sélectionnée
  void setSelectedDate(DateTime date) {
    _selectedDate = date;
    notifyListeners();
  }

  // Changer le type sélectionné
  void setSelectedType(TypeTache? type) {
    _selectedType = type;
    notifyListeners();
  }

  // Changer le statut sélectionné
  void setSelectedStatut(StatutTache? statut) {
    _selectedStatut = statut;
    notifyListeners();
  }

  // Changer la priorité sélectionnée
  void setSelectedPriorite(PrioriteTache? priorite) {
    _selectedPriorite = priorite;
    notifyListeners();
  }

  // Appliquer les filtres
  List<TacheMerchandising> get tachesFiltrees {
    var tachesFiltrees = List<TacheMerchandising>.from(_taches);

    if (_selectedType != null) {
      tachesFiltrees =
          tachesFiltrees.where((t) => t.type == _selectedType).toList();
    }

    if (_selectedStatut != null) {
      tachesFiltrees =
          tachesFiltrees.where((t) => t.statut == _selectedStatut).toList();
    }

    if (_selectedPriorite != null) {
      tachesFiltrees =
          tachesFiltrees.where((t) => t.priorite == _selectedPriorite).toList();
    }

    return tachesFiltrees;
  }

  // Effacer tous les filtres
  void effacerFiltres() {
    _selectedType = null;
    _selectedStatut = null;
    _selectedPriorite = null;
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void effacerErreur() {
    _error = null;
    notifyListeners();
  }

  // Charger les tâches pour une mission spécifique
  Future<void> chargerTachesPourMission(String missionId) async {
    _setLoading(true);
    try {
      // Simuler le chargement des tâches pour une mission
      // En réalité, vous feriez un appel à votre service
      _taches =
          _taches
              .where((t) => t.merchandiserId.toString() == missionId)
              .toList();
      _error = null;
    } catch (e) {
      _error = 'Erreur lors du chargement des tâches: $e';
    } finally {
      _setLoading(false);
    }
  }
}
