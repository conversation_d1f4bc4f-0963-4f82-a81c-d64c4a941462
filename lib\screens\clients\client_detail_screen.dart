import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/client.dart';
import '../../models/commande.dart';
import '../../providers/commande_provider.dart';
import 'client_form_screen.dart';

class ClientDetailScreen extends StatefulWidget {
  final Client client;

  const ClientDetailScreen({super.key, required this.client});

  @override
  State<ClientDetailScreen> createState() => _ClientDetailScreenState();
}

class _ClientDetailScreenState extends State<ClientDetailScreen> {
  @override
  void initState() {
    super.initState();
    // Temporairement désactivé pour éviter les erreurs setState
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _chargerCommandesClient();
    // });
  }

  Future<void> _chargerCommandesClient() async {
    await context.read<CommandeProvider>().chargerCommandesClient(
      widget.client.id!,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.client.nomComplet),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _naviguerVersModification(),
          ),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildInfoCard(),
          const SizedBox(height: 16),
          _buildCommandesCard(),
        ],
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Text(
                    _getInitials(widget.client),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.client.nomComplet,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      Text(
                        'Client depuis le ${DateFormat('dd/MM/yyyy').format(widget.client.primaryCreationDate)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildInfoRow(Icons.email, 'Email', widget.client.email),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.phone, 'Téléphone', widget.client.primaryPhone),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.location_on, 'Adresse', widget.client.adresse),

            // Afficher les nouveaux champs s'ils sont renseignés
            if (widget.client.codeClient?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                Icons.badge,
                'Code client',
                widget.client.codeClient!,
              ),
            ],
            if (widget.client.categorie?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                Icons.category,
                'Catégorie',
                widget.client.categorie!,
              ),
            ],
            if (widget.client.matriculeFiscal?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                Icons.receipt_long,
                'Matricule fiscal',
                widget.client.matriculeFiscal!,
              ),
            ],
            if (widget.client.modeReglement?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                Icons.payment,
                'Mode de règlement',
                widget.client.modeReglement!,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(value, style: Theme.of(context).textTheme.bodyMedium),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCommandesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Commandes', style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Consumer<CommandeProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                if (provider.error != null) {
                  return Center(
                    child: Column(
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 8),
                        Text(provider.error!, textAlign: TextAlign.center),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: _chargerCommandesClient,
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  );
                }

                final commandes = provider.commandes;
                if (commandes.isEmpty) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(32.0),
                      child: Column(
                        children: [
                          Icon(
                            Icons.shopping_cart_outlined,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Aucune commande',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          Text(
                            'Ce client n\'a pas encore passé de commande',
                            style: Theme.of(context).textTheme.bodyMedium
                                ?.copyWith(color: Colors.grey[600]),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return Column(
                  children: [
                    // Statistiques rapides
                    Row(
                      children: [
                        Expanded(
                          child: _buildStatChip(
                            'Total commandes',
                            commandes.length.toString(),
                            Colors.blue,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: _buildStatChip(
                            'Montant total',
                            '${commandes.fold(0.0, (sum, c) => sum + c.montantTotal).toStringAsFixed(2)}€',
                            Colors.green,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    // Liste des commandes
                    ...commandes
                        .take(5)
                        .map((commande) => _buildCommandeItem(commande)),
                    if (commandes.length > 5)
                      TextButton(
                        onPressed: () {
                          // Navigation vers la liste complète des commandes
                        },
                        child: Text(
                          'Voir toutes les commandes (${commandes.length})',
                        ),
                      ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(fontSize: 12, color: color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCommandeItem(Commande commande) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getStatutColor(commande.statut),
        child: Text(
          '#${commande.id ?? ""}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      title: Text(
        'Commande #${commande.id ?? ""}',
        style: const TextStyle(fontWeight: FontWeight.w600),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(DateFormat('dd/MM/yyyy à HH:mm').format(commande.dateCommande)),
          Text(
            commande.statutFormate,
            style: TextStyle(
              color: _getStatutColor(commande.statut),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      trailing: Text(
        commande.montantFormate,
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
      ),
      onTap: () {
        // Navigation vers le détail de la commande
      },
    );
  }

  Color _getStatutColor(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return Colors.orange;
      case StatutCommande.confirmee:
        return Colors.blue;
      case StatutCommande.enPreparation:
        return Colors.purple;
      case StatutCommande.expediee:
        return Colors.indigo;
      case StatutCommande.livree:
        return Colors.green;
      case StatutCommande.annulee:
        return Colors.red;
    }
  }

  void _naviguerVersModification() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ClientFormScreen(client: widget.client),
      ),
    ).then((_) {
      // Recharger les données si nécessaire
      setState(() {});
    });
  }

  String _getInitials(Client client) {
    // Safely get first character of first name if available
    String firstInitial = '';
    if (client.prenom?.isNotEmpty == true) {
      firstInitial = client.prenom![0].toUpperCase();
    }

    // Safely get first character of last name if available
    String lastInitial = '';
    if (client.nom?.isNotEmpty == true) {
      lastInitial = client.nom![0].toUpperCase();
    }

    // Return initials or a fallback character if both are empty
    final initials = '$firstInitial$lastInitial';
    return initials.isNotEmpty ? initials : '?';
  }
}
