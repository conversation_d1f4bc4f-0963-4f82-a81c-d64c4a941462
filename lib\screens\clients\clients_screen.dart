import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../../providers/firebase_client_provider.dart';
import '../../models/client.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../widgets/professional_ui_components.dart';
import 'client_form_screen.dart';
import 'client_detail_screen.dart';

class ClientsScreen extends StatefulWidget {
  const ClientsScreen({super.key});

  @override
  State<ClientsScreen> createState() => _ClientsScreenState();
}

class _ClientsScreenState extends State<ClientsScreen> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Row(
                children: [
                  VitaBrosseLogo(
                    height: isSmallScreen ? 24 : 28,
                    showText: false,
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Clients',
                        style: TextStyle(
                          fontWeight: FontWeight.w700,
                          color: const Color(0xFF1F2937),
                          fontSize: isSmallScreen ? 18 : 20,
                        ),
                      ),
                      Text(
                        'Base clients VitaBrosse',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w400,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 20 : 30,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF3B82F6).withValues(alpha: 0.1),
                              const Color(0xFF1D4ED8).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.people_outline,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFF3B82F6),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Container(
                margin: EdgeInsets.only(right: isSmallScreen ? 12 : 16, top: 8),
                child: FilledButton.icon(
                  onPressed: () => _naviguerVersFormulaire(context),
                  icon: Icon(Icons.add, size: isSmallScreen ? 16 : 18),
                  label: Text(isSmallScreen ? 'Nouveau' : 'Nouveau'),
                  style: FilledButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 12 : 16,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                    backgroundColor: const Color(0xFF3B82F6),
                    textStyle: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                  ),
                ),
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.fromLTRB(padding, 16, padding, 8),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
                  border: Border.all(color: Colors.grey.shade200),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      blurRadius: 10,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'Rechercher un client...',
                    hintStyle: TextStyle(
                      color: Colors.grey.shade500,
                      fontSize: isSmallScreen ? 14 : 16,
                    ),
                    prefixIcon: Icon(
                      Icons.search,
                      color: Colors.grey.shade400,
                      size: isSmallScreen ? 20 : 24,
                    ),
                    suffixIcon:
                        _searchController.text.isNotEmpty
                            ? IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: Colors.grey.shade400,
                              ),
                              onPressed: () {
                                _searchController.clear();
                                context
                                    .read<FirebaseClientProvider>()
                                    .loadClients();
                              },
                            )
                            : null,
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 16 : 20,
                      vertical: isSmallScreen ? 12 : 16,
                    ),
                  ),
                  onChanged: (value) {
                    if (value.isEmpty) {
                      context.read<FirebaseClientProvider>().loadClients();
                    } else {
                      context.read<FirebaseClientProvider>().searchClients(
                        value,
                      );
                    }
                  },
                ),
              ),
            ),
          ),
          Consumer<FirebaseClientProvider>(
            builder: (context, provider, child) {
              if (provider.isLoading) {
                return const SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CircularProgressIndicator(strokeWidth: 3),
                        SizedBox(height: 16),
                        Text(
                          'Chargement des clients...',
                          style: TextStyle(color: Colors.grey, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                );
              }

              if (provider.error != null) {
                return SliverFillRemaining(
                  child: Center(
                    child: Padding(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              color: Colors.red.shade50,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              Icons.error_outline,
                              size: 48,
                              color: Colors.red.shade400,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Text(
                            'Oups ! Une erreur est survenue',
                            style: Theme.of(
                              context,
                            ).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: const Color(0xFF1F2937),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            provider.error!,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: 24),
                          FilledButton.icon(
                            onPressed: () {
                              provider.effacerErreur();
                              provider.chargerClients();
                            },
                            icon: const Icon(Icons.refresh),
                            label: const Text('Réessayer'),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }
              if (provider.clients.isEmpty) {
                return SliverFillRemaining(
                  child: ModernEmptyState(
                    icon: Icons.people_outline,
                    title: 'Aucun client trouvé',
                    subtitle: 'Commencez par ajouter votre premier client',
                    actionText: 'Ajouter un client',
                    onAction: () => _naviguerVersFormulaire(context),
                  ),
                );
              }

              return SliverPadding(
                padding: EdgeInsets.fromLTRB(padding, 8, padding, 20),
                sliver: SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final client = provider.clients[index];
                    return AnimationConfiguration.staggeredList(
                      position: index,
                      duration: const Duration(milliseconds: 375),
                      child: SlideAnimation(
                        verticalOffset: 50.0,
                        child: FadeInAnimation(
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: _buildModernClientCard(context, client),
                          ),
                        ),
                      ),
                    );
                  }, childCount: provider.clients.length),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildModernClientCard(BuildContext context, Client client) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final isVerySmallScreen = screenWidth < 360;
    final avatarSize = isVerySmallScreen ? 48.0 : (isSmallScreen ? 52.0 : 56.0);
    final titleFontSize =
        isVerySmallScreen ? 14.0 : (isSmallScreen ? 15.0 : 16.0);
    final subtitleFontSize =
        isVerySmallScreen ? 12.0 : (isSmallScreen ? 13.0 : 14.0);
    final detailFontSize =
        isVerySmallScreen ? 11.0 : (isSmallScreen ? 12.0 : 13.0);
    final iconSize = isVerySmallScreen ? 14.0 : (isSmallScreen ? 15.0 : 16.0);
    return ProfessionalCard(
      onTap: () => _naviguerVersDetail(context, client),
      child: Row(
        children: [
          Container(
            width: avatarSize,
            height: avatarSize,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(isSmallScreen ? 12 : 16),
            ),
            child: Center(
              child: Text(
                _getInitials(client),
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: isVerySmallScreen ? 14 : (isSmallScreen ? 16 : 18),
                ),
              ),
            ),
          ),
          SizedBox(width: isSmallScreen ? 12 : 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  client.nomComplet,
                  style: TextStyle(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF1F2937),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: isVerySmallScreen ? 2 : 4),
                Text(
                  client.adresse,
                  style: TextStyle(
                    fontSize: subtitleFontSize,
                    color: Colors.grey.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                SizedBox(height: isVerySmallScreen ? 2 : 4),
                Row(
                  children: [
                    Icon(
                      Icons.email_outlined,
                      size: iconSize,
                      color: Colors.grey.shade500,
                    ),
                    SizedBox(width: isVerySmallScreen ? 2 : 4),
                    Expanded(
                      child: Text(
                        client.email,
                        style: TextStyle(
                          fontSize: detailFontSize,
                          color: Colors.grey.shade500,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: isVerySmallScreen ? 1 : 2),
                Row(
                  children: [
                    Icon(
                      Icons.phone_outlined,
                      size: iconSize,
                      color: Colors.grey.shade500,
                    ),
                    SizedBox(width: isVerySmallScreen ? 2 : 4),
                    Text(
                      client.primaryPhone,
                      style: TextStyle(
                        fontSize: detailFontSize,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.all(isVerySmallScreen ? 6 : 8),
            decoration: BoxDecoration(
              color: const Color(0xFF3B82F6).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.arrow_forward_ios,
              size: isVerySmallScreen ? 12 : 16,
              color: const Color(0xFF3B82F6),
            ),
          ),
        ],
      ),
    );
  }

  void _naviguerVersFormulaire(BuildContext context, {Client? client}) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => ClientFormScreen(client: client)),
    );
  }

  void _naviguerVersDetail(BuildContext context, Client client) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ClientDetailScreen(client: client),
      ),
    );
  }

  String _getInitials(Client client) {
    // Safely get first character of first name if available
    String firstInitial = '';
    if (client.prenom?.isNotEmpty == true) {
      firstInitial = client.prenom![0].toUpperCase();
    }

    // Safely get first character of last name if available
    String lastInitial = '';
    if (client.nom?.isNotEmpty == true) {
      lastInitial = client.nom![0].toUpperCase();
    }

    // Return initials or a fallback character if both are empty
    final initials = '$firstInitial$lastInitial';
    return initials.isNotEmpty ? initials : '?';
  }
}
