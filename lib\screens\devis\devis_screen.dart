import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/devis.dart';
import '../../providers/firebase_client_provider.dart';
import '../../services/whatsapp_service.dart';
import '../../widgets/whatsapp_message_dialog.dart';
import '../../widgets/vitabrosse_logo.dart';
import '../../main.dart' show DevisProvider;
import 'nouveau_devis_screen.dart';
import 'devis_detail_screen.dart';

class DevisScreen extends StatefulWidget {
  const DevisScreen({super.key});

  @override
  State<DevisScreen> createState() => _DevisScreenState();
}

class _DevisScreenState extends State<DevisScreen> {
  StatutDevis? _filtreStatut;
  final TextEditingController _rechercheController = TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<DevisProvider>().chargerDevis();
    });
  }

  @override
  void dispose() {
    _rechercheController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: isSmallScreen ? 120 : 140,
            floating: false,
            pinned: true,
            backgroundColor: Colors.white,
            surfaceTintColor: Colors.white,
            elevation: 0,
            scrolledUnderElevation: 2,
            flexibleSpace: FlexibleSpaceBar(
              titlePadding: EdgeInsets.only(left: padding, bottom: 16),
              title: Row(
                children: [
                  const VitaBrosseLogo(height: 28, showText: false),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Devis',
                          style: TextStyle(
                            fontWeight: FontWeight.w700,
                            color: const Color(0xFF1F2937),
                            fontSize: isSmallScreen ? 18 : 22,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          'Propositions commerciales',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      const Color(0xFF3B82F6).withValues(alpha: 0.05),
                      const Color(0xFF1D4ED8).withValues(alpha: 0.05),
                    ],
                    stops: const [0.0, 0.7, 1.0],
                  ),
                ),
                child: Stack(
                  children: [
                    Positioned(
                      top: isSmallScreen ? 20 : 30,
                      right: isSmallScreen ? 10 : 15,
                      child: Container(
                        width: isSmallScreen ? 60 : 80,
                        height: isSmallScreen ? 60 : 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              const Color(0xFF3B82F6).withValues(alpha: 0.1),
                              const Color(0xFF1D4ED8).withValues(alpha: 0.1),
                            ],
                          ),
                        ),
                        child: Center(
                          child: Icon(
                            Icons.description_outlined,
                            size: isSmallScreen ? 24 : 32,
                            color: const Color(0xFF3B82F6),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              Container(
                margin: EdgeInsets.only(right: isSmallScreen ? 8 : 12, top: 8),
                child: IconButton(
                  icon: Icon(Icons.refresh, size: isSmallScreen ? 20 : 24),
                  onPressed: () => context.read<DevisProvider>().chargerDevis(),
                  tooltip: 'Actualiser',
                ),
              ),
              Container(
                margin: EdgeInsets.only(right: isSmallScreen ? 12 : 16, top: 8),
                child: FilledButton.icon(
                  onPressed: () => _naviguerVersNouveauDevis(),
                  icon: Icon(Icons.add, size: isSmallScreen ? 16 : 18),
                  label: Text(isSmallScreen ? 'Nouveau' : 'Nouveau'),
                  style: FilledButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 12 : 16,
                      vertical: isSmallScreen ? 6 : 8,
                    ),
                    backgroundColor: const Color(0xFF3B82F6),
                    textStyle: TextStyle(fontSize: isSmallScreen ? 12 : 14),
                  ),
                ),
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.fromLTRB(padding, 16, padding, 8),
              child: Column(
                children: [
                  // Champ de recherche optimisé
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.grey[200]!),
                    ),
                    child: TextField(
                      controller: _rechercheController,
                      style: TextStyle(fontSize: isSmallScreen ? 14 : 16),
                      decoration: InputDecoration(
                        hintText: 'Rechercher un devis...',
                        hintStyle: TextStyle(
                          color: Colors.grey[500],
                          fontSize: isSmallScreen ? 14 : 16,
                        ),
                        prefixIcon: Icon(
                          Icons.search,
                          color: Colors.grey[500],
                          size: isSmallScreen ? 20 : 24,
                        ),
                        suffixIcon:
                            _rechercheController.text.isNotEmpty
                                ? IconButton(
                                  icon: Icon(
                                    Icons.clear,
                                    size: isSmallScreen ? 18 : 20,
                                  ),
                                  onPressed: () {
                                    _rechercheController.clear();
                                    setState(() {});
                                  },
                                )
                                : null,
                        border: InputBorder.none,
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: isSmallScreen ? 12 : 16,
                          vertical: isSmallScreen ? 12 : 16,
                        ),
                      ),
                      onChanged: (value) => setState(() {}),
                    ),
                  ),
                  SizedBox(height: isSmallScreen ? 12 : 16),

                  // Filtres par statut optimisés
                  SizedBox(
                    height: isSmallScreen ? 36 : 40,
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: [
                        _buildFiltreChip('Tous', null, isSmallScreen),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        _buildFiltreChip(
                          'Brouillon',
                          StatutDevis.brouillon,
                          isSmallScreen,
                        ),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        _buildFiltreChip(
                          'Envoyé',
                          StatutDevis.envoye,
                          isSmallScreen,
                        ),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        _buildFiltreChip(
                          'Accepté',
                          StatutDevis.accepte,
                          isSmallScreen,
                        ),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        _buildFiltreChip(
                          'Refusé',
                          StatutDevis.refuse,
                          isSmallScreen,
                        ),
                        SizedBox(width: isSmallScreen ? 6 : 8),
                        _buildFiltreChip(
                          'Expiré',
                          StatutDevis.expire,
                          isSmallScreen,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Liste des devis
          SliverFillRemaining(
            child: Consumer<DevisProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (provider.error != null) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[300],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Erreur',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          provider.error!,
                          textAlign: TextAlign.center,
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => provider.chargerDevis(),
                          child: const Text('Réessayer'),
                        ),
                      ],
                    ),
                  );
                }

                List<Devis> devisFiltres = provider.devis;

                // Appliquer le filtre de recherche
                if (_rechercheController.text.isNotEmpty) {
                  final terme = _rechercheController.text.toLowerCase();
                  devisFiltres =
                      devisFiltres
                          .where(
                            (devis) =>
                                devis.numero.toLowerCase().contains(terme) ||
                                devis.clientId.toLowerCase().contains(terme),
                          )
                          .toList();
                }

                // Appliquer le filtre de statut
                if (_filtreStatut != null) {
                  devisFiltres =
                      devisFiltres
                          .where((d) => d.statut == _filtreStatut)
                          .toList();
                }

                if (devisFiltres.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.description_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Aucun devis trouvé',
                          style: Theme.of(context).textTheme.headlineSmall,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Créez votre premier devis',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: EdgeInsets.symmetric(horizontal: padding),
                  itemCount: devisFiltres.length,
                  itemBuilder: (context, index) {
                    final devis = devisFiltres[index];
                    return _buildDevisCard(devis, isSmallScreen);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltreChip(
    String label,
    StatutDevis? statut,
    bool isSmallScreen,
  ) {
    final isSelected = _filtreStatut == statut;
    return FilterChip(
      label: Text(label, style: TextStyle(fontSize: isSmallScreen ? 12 : 14)),
      selected: isSelected,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 8 : 12,
        vertical: isSmallScreen ? 4 : 6,
      ),
      onSelected: (selected) {
        setState(() {
          _filtreStatut = selected ? statut : null;
        });
      },
      selectedColor: Theme.of(context).colorScheme.primaryContainer,
    );
  }

  Widget _buildDevisCard(Devis devis, bool isSmallScreen) {
    return Card(
      margin: EdgeInsets.only(bottom: isSmallScreen ? 8 : 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _naviguerVersDetail(devis),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header avec numéro et statut
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    devis.numeroFormate,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: isSmallScreen ? 16 : 18,
                      color: const Color(0xFF1F2937),
                    ),
                  ),
                  Row(
                    children: [
                      _buildStatutChip(devis.statut, isSmallScreen),
                      SizedBox(width: isSmallScreen ? 4 : 8),
                      PopupMenuButton<String>(
                        onSelected: (value) => _gererActionDevis(value, devis),
                        icon: Icon(
                          Icons.more_vert,
                          size: isSmallScreen ? 18 : 20,
                        ),
                        itemBuilder:
                            (context) => [
                              const PopupMenuItem(
                                value: 'voir',
                                child: Row(
                                  children: [
                                    Icon(Icons.visibility, size: 18),
                                    SizedBox(width: 8),
                                    Text('Voir'),
                                  ],
                                ),
                              ),
                              if (devis.peutEtreModifie)
                                const PopupMenuItem(
                                  value: 'modifier',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit, size: 18),
                                      SizedBox(width: 8),
                                      Text('Modifier'),
                                    ],
                                  ),
                                ),
                              const PopupMenuItem(
                                value: 'dupliquer',
                                child: Row(
                                  children: [
                                    Icon(Icons.copy, size: 18),
                                    SizedBox(width: 8),
                                    Text('Dupliquer'),
                                  ],
                                ),
                              ),
                              if (devis.statut == StatutDevis.envoye)
                                const PopupMenuItem(
                                  value: 'accepter',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.check_circle,
                                        color: Colors.green,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text('Accepter'),
                                    ],
                                  ),
                                ),
                              if (devis.statut == StatutDevis.envoye)
                                const PopupMenuItem(
                                  value: 'refuser',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.cancel,
                                        color: Colors.red,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text('Refuser'),
                                    ],
                                  ),
                                ),
                              if (devis.peutEtreModifie)
                                const PopupMenuItem(
                                  value: 'supprimer',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.delete,
                                        color: Colors.red,
                                        size: 18,
                                      ),
                                      SizedBox(width: 8),
                                      Text(
                                        'Supprimer',
                                        style: TextStyle(color: Colors.red),
                                      ),
                                    ],
                                  ),
                                ),
                            ],
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 8 : 12),

              // Informations du devis
              Row(
                children: [
                  Icon(
                    Icons.calendar_today_outlined,
                    size: isSmallScreen ? 14 : 16,
                    color: Colors.grey[600],
                  ),
                  SizedBox(width: 4),
                  Text(
                    'Créé: ${DateFormat('dd/MM/yyyy').format(devis.dateCreation)}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 4 : 6),
              Row(
                children: [
                  Icon(
                    Icons.access_time_outlined,
                    size: isSmallScreen ? 14 : 16,
                    color: devis.estExpire ? Colors.red : Colors.grey[600],
                  ),
                  SizedBox(width: 4),
                  Text(
                    'Expire: ${DateFormat('dd/MM/yyyy').format(devis.dateExpiration)}',
                    style: TextStyle(
                      color: devis.estExpire ? Colors.red : Colors.grey[600],
                      fontWeight:
                          devis.estExpire ? FontWeight.w600 : FontWeight.normal,
                      fontSize: isSmallScreen ? 12 : 14,
                    ),
                  ),
                ],
              ),
              SizedBox(height: isSmallScreen ? 8 : 12),

              // Footer avec articles et montant
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.shopping_cart_outlined,
                        size: isSmallScreen ? 14 : 16,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 4),
                      Text(
                        '${devis.nombreArticles} article(s)',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: isSmallScreen ? 12 : 14,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    devis.totalTTCFormate,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: const Color(0xFF3B82F6),
                      fontSize: isSmallScreen ? 16 : 18,
                    ),
                  ),
                ],
              ),

              if (devis.peutEtreEnvoye ||
                  devis.peutEtreTransformeEnCommande) ...[
                SizedBox(height: isSmallScreen ? 8 : 12),
                // Actions rapides
                if (isSmallScreen) ...[
                  // Mode mobile - boutons empilés
                  Column(
                    children: [
                      if (devis.peutEtreEnvoye) ...[
                        Row(
                          children: [
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () => _envoyerDevis(devis),
                                icon: Icon(Icons.send, size: 16),
                                label: Text(
                                  'Envoyer',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: OutlinedButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                ),
                              ),
                            ),
                            SizedBox(width: 8),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed:
                                    () => _envoyerDevisParWhatsApp(devis),
                                icon: Icon(
                                  Icons.chat,
                                  size: 16,
                                  color: Color(0xFF25D366),
                                ),
                                label: Text(
                                  'WhatsApp',
                                  style: TextStyle(fontSize: 12),
                                ),
                                style: OutlinedButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 8),
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (devis.peutEtreTransformeEnCommande)
                          SizedBox(height: 8),
                      ],
                      if (devis.peutEtreTransformeEnCommande)
                        SizedBox(
                          width: double.infinity,
                          child: FilledButton.icon(
                            onPressed: () => _transformerEnCommande(devis),
                            icon: Icon(Icons.shopping_cart, size: 16),
                            label: Text(
                              'Transformer en commande',
                              style: TextStyle(fontSize: 12),
                            ),
                            style: FilledButton.styleFrom(
                              backgroundColor: const Color(0xFFF59E0B),
                              padding: EdgeInsets.symmetric(vertical: 10),
                            ),
                          ),
                        ),
                    ],
                  ),
                ] else ...[
                  // Mode desktop - boutons en ligne
                  Wrap(
                    spacing: 8,
                    children: [
                      if (devis.peutEtreEnvoye) ...[
                        TextButton.icon(
                          onPressed: () => _envoyerDevis(devis),
                          icon: const Icon(Icons.send, size: 16),
                          label: const Text('Envoyer'),
                        ),
                        TextButton.icon(
                          onPressed: () => _envoyerDevisParWhatsApp(devis),
                          icon: const Icon(
                            Icons.chat,
                            size: 16,
                            color: Color(0xFF25D366),
                          ),
                          label: const Text('WhatsApp'),
                        ),
                      ],
                      if (devis.peutEtreTransformeEnCommande)
                        TextButton.icon(
                          onPressed: () => _transformerEnCommande(devis),
                          icon: const Icon(Icons.shopping_cart, size: 16),
                          label: const Text('Commande'),
                        ),
                    ],
                  ),
                ],
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatutChip(StatutDevis statut, bool isSmallScreen) {
    Color couleur;
    switch (statut) {
      case StatutDevis.brouillon:
        couleur = Colors.grey;
        break;
      case StatutDevis.envoye:
        couleur = Colors.blue;
        break;
      case StatutDevis.accepte:
        couleur = Colors.green;
        break;
      case StatutDevis.refuse:
        couleur = Colors.red;
        break;
      case StatutDevis.expire:
        couleur = Colors.orange;
        break;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isSmallScreen ? 6 : 8,
        vertical: isSmallScreen ? 3 : 4,
      ),
      decoration: BoxDecoration(
        color: couleur.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: couleur),
      ),
      child: Text(
        _getStatutLabel(statut),
        style: TextStyle(
          color: couleur,
          fontSize: isSmallScreen ? 10 : 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String _getStatutLabel(StatutDevis statut) {
    switch (statut) {
      case StatutDevis.brouillon:
        return 'Brouillon';
      case StatutDevis.envoye:
        return 'Envoyé';
      case StatutDevis.accepte:
        return 'Accepté';
      case StatutDevis.refuse:
        return 'Refusé';
      case StatutDevis.expire:
        return 'Expiré';
    }
  }

  void _naviguerVersNouveauDevis() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NouveauDevisScreen()),
    );
  }

  void _naviguerVersDetail(Devis devis) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => DevisDetailScreen(devis: devis)),
    );
  }

  void _envoyerDevis(Devis devis) async {
    final success = await context.read<DevisProvider>().mettreAJourStatut(
      devis.id!,
      'envoye',
    );

    if (success) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('Devis envoyé avec succès')));
    }
  }

  void _transformerEnCommande(Devis devis) async {
    // Afficher une boîte de dialogue de confirmation
    final bool? confirme = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Transformer en commande'),
            content: Text(
              'Voulez-vous transformer le devis ${devis.numeroFormate} en commande ?\n\n'
              'Cette action créera une nouvelle commande avec les mêmes articles.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Annuler'),
              ),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Transformer'),
              ),
            ],
          ),
    );

    if (confirme == true && mounted) {
      final commandeId = await context
          .read<DevisProvider>()
          .transformerEnCommande(devis.id!);

      if (commandeId != null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Devis transformé en commande avec succès'),
            backgroundColor: Colors.green,
            action: SnackBarAction(
              label: 'Voir les commandes',
              textColor: Colors.white,
              onPressed: () {
                // TODO: Navigation vers l'écran des commandes avec le bon ID
                Navigator.of(context).pushNamed('/commandes');
              },
            ),
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la transformation'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _gererActionDevis(String action, Devis devis) {
    switch (action) {
      case 'voir':
        _naviguerVersDetail(devis);
        break;
      case 'modifier':
        // TODO: Implémenter la modification
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Modification en cours de développement'),
          ),
        );
        break;
      case 'dupliquer':
        _dupliquerDevis(devis);
        break;
      case 'accepter':
        _changerStatutDevis(devis, StatutDevis.accepte);
        break;
      case 'refuser':
        _changerStatutDevis(devis, StatutDevis.refuse);
        break;
      case 'supprimer':
        _confirmerSuppression(devis);
        break;
    }
  }

  void _dupliquerDevis(Devis devis) async {
    final nouveauDevisId = await context.read<DevisProvider>().dupliquerDevis(
      devis.id!,
    );

    if (nouveauDevisId != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Devis dupliqué avec succès')),
      );
    }
  }

  void _confirmerSuppression(Devis devis) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer le devis ${devis.numeroFormate} ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  final success = await context
                      .read<DevisProvider>()
                      .supprimerDevis(devis.id!);
                  if (success && mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Devis supprimé avec succès'),
                      ),
                    );
                  }
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Supprimer'),
              ),
            ],
          ),
    );
  }

  void _changerStatutDevis(Devis devis, StatutDevis nouveauStatut) async {
    String action =
        nouveauStatut == StatutDevis.accepte ? 'accepter' : 'refuser';
    String titre =
        nouveauStatut == StatutDevis.accepte
            ? 'Accepter le devis'
            : 'Refuser le devis';

    final bool? confirme = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(titre),
            content: Text(
              'Voulez-vous vraiment $action le devis ${devis.numeroFormate} ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('Annuler'),
              ),
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: FilledButton.styleFrom(
                  backgroundColor:
                      nouveauStatut == StatutDevis.accepte
                          ? Colors.green
                          : Colors.red,
                ),
                child: Text(
                  nouveauStatut == StatutDevis.accepte ? 'Accepter' : 'Refuser',
                ),
              ),
            ],
          ),
    );

    if (confirme == true && mounted) {
      final success = await context.read<DevisProvider>().mettreAJourStatut(
        devis.id!,
        nouveauStatut == StatutDevis.accepte ? 'accepte' : 'refuse',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              nouveauStatut == StatutDevis.accepte
                  ? 'Devis accepté avec succès'
                  : 'Devis refusé',
            ),
            backgroundColor:
                nouveauStatut == StatutDevis.accepte
                    ? Colors.green
                    : Colors.orange,
          ),
        );
      } else if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Erreur lors de la mise à jour du statut'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _envoyerDevisParWhatsApp(Devis devis) async {
    try {
      // Charger les clients seulement si nécessaire
      final clientProvider = context.read<FirebaseClientProvider>();
      if (!clientProvider.isLoaded) {
        await Future.microtask(() async {
          await clientProvider.chargerClients();
        });
      }

      final clients = clientProvider.clients;
      final client = clients.firstWhere(
        (c) => c.id == devis.clientId,
        orElse: () => throw Exception('Client introuvable'),
      );

      if (client.primaryPhone.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Numéro de téléphone du client manquant'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Générer l'aperçu du message
      final messageParDefaut =
          WhatsAppService.genererMessageDevis(
            devis,
            client,
            null,
          ).split('\n').take(3).join('\n') +
          '...';

      if (mounted) {
        // Afficher la boîte de dialogue pour personnaliser le message
        final messagePersonnalise = await showDialog<String>(
          context: context,
          builder:
              (context) => WhatsAppMessageDialog(
                titre: 'Envoyer le devis par WhatsApp',
                messageParDefaut: messageParDefaut,
                client: client,
                onEnvoyer: () {},
              ),
        );

        // Vérifier si l'utilisateur n'a pas annulé
        if (messagePersonnalise == null) {
          return; // L'utilisateur a cliqué sur Annuler
        }

        // Envoyer le devis (chaîne vide = message par défaut, sinon message personnalisé)
        final messageAUtiliser =
            messagePersonnalise.isEmpty ? null : messagePersonnalise;
        final success = await WhatsAppService.envoyerDevis(
          devis: devis,
          client: client,
          messagePersonnalise: messageAUtiliser,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success
                    ? 'WhatsApp ouvert avec le devis'
                    : 'Erreur lors de l\'ouverture de WhatsApp',
              ),
              backgroundColor: success ? Colors.green : Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
