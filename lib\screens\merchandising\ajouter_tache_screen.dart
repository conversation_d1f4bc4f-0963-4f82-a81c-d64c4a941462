import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/tache_merchandising.dart';
import '../../providers/tache_merchandising_provider.dart';

class AjouterTacheScreen extends StatefulWidget {
  final String missionId;
  final TacheMerchandising? tache;

  const AjouterTacheScreen({
    super.key,
    required this.missionId,
    this.tache,
  });

  @override
  State<AjouterTacheScreen> createState() => _AjouterTacheScreenState();
}

class _AjouterTacheScreenState extends State<AjouterTacheScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titreController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  String _priorite = 'normale';
  DateTime? _dateEcheance;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.tache != null) {
      _titreController.text = widget.tache!.titre;
      _descriptionController.text = widget.tache!.description ?? '';
      _priorite = widget.tache!.priorite;
      _dateEcheance = widget.tache!.dateEcheance;
    }
  }

  @override
  void dispose() {
    _titreController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.tache == null ? 'Ajouter une tâche' : 'Modifier la tâche'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Titre
              TextFormField(
                controller: _titreController,
                decoration: const InputDecoration(
                  labelText: 'Titre de la tâche',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Veuillez saisir un titre';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Description
              TextFormField(
                controller: _descriptionController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Description (optionnel)',
                  border: OutlineInputBorder(),
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Priorité
              DropdownButtonFormField<String>(
                value: _priorite,
                decoration: const InputDecoration(
                  labelText: 'Priorité',
                  border: OutlineInputBorder(),
                ),
                items: const [
                  DropdownMenuItem(value: 'faible', child: Text('Faible')),
                  DropdownMenuItem(value: 'normale', child: Text('Normale')),
                  DropdownMenuItem(value: 'haute', child: Text('Haute')),
                  DropdownMenuItem(value: 'urgente', child: Text('Urgente')),
                ],
                onChanged: (value) {
                  setState(() {
                    _priorite = value!;
                  });
                },
              ),
              
              const SizedBox(height: 16),
              
              // Date d'échéance
              ListTile(
                title: Text(_dateEcheance == null 
                    ? 'Sélectionner une date d\'échéance' 
                    : 'Échéance: ${_dateEcheance!.day}/${_dateEcheance!.month}/${_dateEcheance!.year}'),
                leading: const Icon(Icons.calendar_today),
                onTap: _selectionnerDate,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                  side: BorderSide(color: Colors.grey.shade400),
                ),
              ),
              
              const Spacer(),
              
              // Boutons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _sauvegarder,
                      child: _isLoading 
                          ? const CircularProgressIndicator()
                          : Text(widget.tache == null ? 'Ajouter' : 'Modifier'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectionnerDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _dateEcheance ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() {
        _dateEcheance = date;
      });
    }
  }

  Future<void> _sauvegarder() async {
    if (!_formKey.currentState!.validate()) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final provider = Provider.of<TacheMerchandisingProvider>(context, listen: false);
      
      final tache = TacheMerchandising(
        id: widget.tache?.id,
        missionId: widget.missionId,
        titre: _titreController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        priorite: _priorite,
        dateCreation: widget.tache?.dateCreation ?? DateTime.now(),
        dateEcheance: _dateEcheance,
        statut: widget.tache?.statut ?? 'en_attente',
      );

      bool success;
      if (widget.tache == null) {
        success = await provider.ajouterTache(tache);
      } else {
        success = await provider.modifierTache(tache);
      }

      if (success && mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.tache == null 
                ? 'Tâche ajoutée avec succès' 
                : 'Tâche modifiée avec succès'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
