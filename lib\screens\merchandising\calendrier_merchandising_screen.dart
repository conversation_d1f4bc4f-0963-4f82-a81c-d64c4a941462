import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:table_calendar/table_calendar.dart';
import '../../models/tache_merchandising.dart';
import '../../providers/tache_merchandising_provider.dart';
// import 'detail_tache_screen.dart';

class CalendrierMerchandisingScreen extends StatefulWidget {
  const CalendrierMerchandisingScreen({super.key});

  @override
  State<CalendrierMerchandisingScreen> createState() =>
      _CalendrierMerchandisingScreenState();
}

class _CalendrierMerchandisingScreenState
    extends State<CalendrierMerchandisingScreen> {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerTaches();
    });
  }

  void _chargerTaches() {
    final provider = Provider.of<TacheMerchandisingProvider>(
      context,
      listen: false,
    );
    provider.chargerTaches();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Calendrier Merchandising'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _chargerTaches,
          ),
        ],
      ),
      body: Consumer<TacheMerchandisingProvider>(
        builder: (context, provider, child) {
          return Column(
            children: [
              // Calendrier
              Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: TableCalendar<TacheMerchandising>(
                  firstDay: DateTime.utc(2020, 1, 1),
                  lastDay: DateTime.utc(2030, 12, 31),
                  focusedDay: _focusedDay,
                  calendarFormat: _calendarFormat,
                  eventLoader: _getTachesForDay,
                  startingDayOfWeek: StartingDayOfWeek.monday,
                  selectedDayPredicate: (day) {
                    return isSameDay(_selectedDay, day);
                  },
                  onDaySelected: (selectedDay, focusedDay) {
                    if (!isSameDay(_selectedDay, selectedDay)) {
                      setState(() {
                        _selectedDay = selectedDay;
                        _focusedDay = focusedDay;
                      });
                    }
                  },
                  onFormatChanged: (format) {
                    if (_calendarFormat != format) {
                      setState(() {
                        _calendarFormat = format;
                      });
                    }
                  },
                  onPageChanged: (focusedDay) {
                    _focusedDay = focusedDay;
                  },
                  calendarStyle: const CalendarStyle(
                    outsideDaysVisible: false,
                    weekendTextStyle: TextStyle(color: Colors.red),
                    holidayTextStyle: TextStyle(color: Colors.red),
                  ),
                  headerStyle: const HeaderStyle(
                    formatButtonVisible: true,
                    titleCentered: true,
                    formatButtonShowsNext: false,
                    formatButtonDecoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.all(Radius.circular(12.0)),
                    ),
                    formatButtonTextStyle: TextStyle(color: Colors.white),
                  ),
                ),
              ),

              // Liste des tâches du jour sélectionné
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Text(
                          _selectedDay == null
                              ? 'Sélectionnez une date'
                              : 'Tâches du ${_selectedDay!.day}/${_selectedDay!.month}/${_selectedDay!.year}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Expanded(child: _buildTachesList(provider)),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  List<TacheMerchandising> _getTachesForDay(DateTime day) {
    final provider = Provider.of<TacheMerchandisingProvider>(
      context,
      listen: false,
    );
    return provider.taches.where((tache) {
      if (tache.dateEcheance == null) return false;
      return isSameDay(tache.dateEcheance!, day);
    }).toList();
  }

  Widget _buildTachesList(TacheMerchandisingProvider provider) {
    if (provider.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_selectedDay == null) {
      return const Center(
        child: Text('Sélectionnez une date pour voir les tâches'),
      );
    }

    final tachesDuJour = _getTachesForDay(_selectedDay!);

    if (tachesDuJour.isEmpty) {
      return const Center(child: Text('Aucune tâche pour cette date'));
    }

    return ListView.builder(
      itemCount: tachesDuJour.length,
      itemBuilder: (context, index) {
        final tache = tachesDuJour[index];
        return _buildTacheCard(tache);
      },
    );
  }

  Widget _buildTacheCard(TacheMerchandising tache) {
    Color prioriteColor;
    switch (tache.priorite) {
      case PrioriteTache.urgente:
        prioriteColor = Colors.red;
        break;
      case PrioriteTache.haute:
        prioriteColor = Colors.orange;
        break;
      case PrioriteTache.normale:
        prioriteColor = Colors.blue;
        break;
      case PrioriteTache.basse:
        prioriteColor = Colors.green;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 4,
          height: double.infinity,
          color: prioriteColor,
        ),
        title: Text(
          tache.titre,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            decoration:
                tache.statut == 'terminee' ? TextDecoration.lineThrough : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (tache.description != null) ...[
              Text(tache.description!),
              const SizedBox(height: 4),
            ],
            Row(
              children: [
                Icon(Icons.flag, size: 16, color: prioriteColor),
                const SizedBox(width: 4),
                Text(
                  tache.priorite.name.toUpperCase(),
                  style: TextStyle(
                    color: prioriteColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatutColor(tache.statut).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatutText(tache.statut),
                    style: TextStyle(
                      color: _getStatutColor(tache.statut),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DetailTacheScreen(tache: tache),
            ),
          );
        },
      ),
    );
  }

  Color _getStatutColor(StatutTache statut) {
    switch (statut) {
      case StatutTache.terminee:
        return Colors.green;
      case StatutTache.en_cours:
        return Colors.orange;
      case StatutTache.planifiee:
        return Colors.blue;
      case StatutTache.reportee:
        return Colors.orange;
      case StatutTache.annulee:
        return Colors.red;
    }
  }

  String _getStatutText(StatutTache statut) {
    switch (statut) {
      case StatutTache.terminee:
        return 'Terminée';
      case StatutTache.en_cours:
        return 'En cours';
      case StatutTache.planifiee:
        return 'Planifiée';
      case StatutTache.reportee:
        return 'Reportée';
      case StatutTache.annulee:
        return 'Annulée';
    }
  }
}
