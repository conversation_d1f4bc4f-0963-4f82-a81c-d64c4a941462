import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';

import '../../models/mission.dart';
import 'detail_mission_screen.dart';

class CalendrierMissionsScreen extends StatefulWidget {
  @override
  _CalendrierMissionsScreenState createState() =>
      _CalendrierMissionsScreenState();
}

class _CalendrierMissionsScreenState extends State<CalendrierMissionsScreen> {
  DateTime _selectedDate = DateTime.now();

  @override
  void initState() {
    super.initState();
    _chargerMissions();
  }

  void _chargerMissions() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );

    // Simuler un ID de merchandiser - vous devrez adapter selon votre AuthProvider
    final merchandiserId = 'merchandiser_123';

    missionProvider.chargerMissionsParMerchandiser(merchandiserId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Calendrier des missions'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: Consumer<MissionProvider>(
        builder: (context, missionProvider, child) {
          if (missionProvider.isLoading) {
            return Center(child: CircularProgressIndicator());
          }

          if (missionProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, size: 64, color: Colors.red),
                  SizedBox(height: 16),
                  Text(
                    'Erreur: ${missionProvider.error}',
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _chargerMissions,
                    child: Text('Réessayer'),
                  ),
                ],
              ),
            );
          }

          final missions = missionProvider.missions;

          return RefreshIndicator(
            onRefresh: () async {
              _chargerMissions();
            },
            child: Column(
              children: [
                // Navigation du calendrier
                Container(
                  padding: EdgeInsets.all(16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _selectedDate = DateTime(
                              _selectedDate.year,
                              _selectedDate.month - 1,
                              _selectedDate.day,
                            );
                          });
                        },
                        icon: Icon(Icons.chevron_left),
                      ),
                      Text(
                        _getMonthYearString(_selectedDate),
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          setState(() {
                            _selectedDate = DateTime(
                              _selectedDate.year,
                              _selectedDate.month + 1,
                              _selectedDate.day,
                            );
                          });
                        },
                        icon: Icon(Icons.chevron_right),
                      ),
                    ],
                  ),
                ),

                // Calendrier simple
                Expanded(child: _buildCalendar(missions)),

                // Missions du jour sélectionné
                Container(height: 200, child: _buildMissionsDuJour(missions)),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCalendar(List<Mission> missions) {
    final firstDayOfMonth = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      1,
    );
    final startDate = firstDayOfMonth.subtract(
      Duration(days: firstDayOfMonth.weekday - 1),
    );

    return GridView.builder(
      padding: EdgeInsets.all(16),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 7,
        childAspectRatio: 1,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: 42, // 6 semaines * 7 jours
      itemBuilder: (context, index) {
        final date = startDate.add(Duration(days: index));
        final isCurrentMonth = date.month == _selectedDate.month;
        final isToday = _isSameDay(date, DateTime.now());
        final isSelected = _isSameDay(date, _selectedDate);

        // Trouver les missions pour cette date
        final missionsJour =
            missions
                .where((mission) => _isSameDay(mission.dateEcheance, date))
                .toList();

        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedDate = date;
            });
          },
          child: Container(
            decoration: BoxDecoration(
              color:
                  isSelected
                      ? Colors.purple
                      : isToday
                      ? Colors.purple.shade100
                      : Colors.transparent,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color:
                    isCurrentMonth ? Colors.grey.shade300 : Colors.transparent,
              ),
            ),
            child: Stack(
              children: [
                Center(
                  child: Text(
                    date.day.toString(),
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                      color:
                          isSelected
                              ? Colors.white
                              : isCurrentMonth
                              ? Colors.black
                              : Colors.grey.shade400,
                    ),
                  ),
                ),
                if (missionsJour.isNotEmpty)
                  Positioned(
                    right: 4,
                    top: 4,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: _getMissionDotColor(missionsJour),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMissionsDuJour(List<Mission> missions) {
    final missionsJour =
        missions
            .where((mission) => _isSameDay(mission.dateEcheance, _selectedDate))
            .toList();

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Missions du ${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 12),
          Expanded(
            child:
                missionsJour.isEmpty
                    ? Center(
                      child: Text(
                        'Aucune mission pour ce jour',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 16,
                        ),
                      ),
                    )
                    : ListView.builder(
                      itemCount: missionsJour.length,
                      itemBuilder: (context, index) {
                        final mission = missionsJour[index];
                        return _buildMissionItem(mission);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildMissionItem(Mission mission) {
    return Container(
      margin: EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 2,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DetailMissionScreen(mission: mission),
            ),
          );
        },
        child: Row(
          children: [
            Container(
              width: 4,
              height: 40,
              decoration: BoxDecoration(
                color: _getStatutColor(mission.statut),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    mission.titre,
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      decoration:
                          mission.estTerminee
                              ? TextDecoration.lineThrough
                              : null,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    mission.magasinNom,
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
            Chip(
              label: Text(
                mission.statutAffichage,
                style: TextStyle(fontSize: 10),
              ),
              backgroundColor: _getStatutColor(mission.statut).withOpacity(0.2),
            ),
          ],
        ),
      ),
    );
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  String _getMonthYearString(DateTime date) {
    const months = [
      'Janvier',
      'Février',
      'Mars',
      'Avril',
      'Mai',
      'Juin',
      'Juillet',
      'Août',
      'Septembre',
      'Octobre',
      'Novembre',
      'Décembre',
    ];
    return '${months[date.month - 1]} ${date.year}';
  }

  Color _getMissionDotColor(List<Mission> missions) {
    if (missions.any((m) => m.estEnRetard)) {
      return Colors.red;
    } else if (missions.any((m) => m.priorite == 'urgente')) {
      return Colors.orange;
    } else if (missions.any((m) => m.statut == 'en_cours')) {
      return Colors.blue;
    } else if (missions.any((m) => m.statut == 'terminee')) {
      return Colors.green;
    }
    return Colors.grey;
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'en_attente':
        return Colors.orange;
      case 'en_cours':
        return Colors.blue;
      case 'terminee':
        return Colors.green;
      case 'annulee':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
