import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../models/rapport_merchandising.dart';
import '../../providers/rapport_provider.dart';
import '../../providers/mission_provider.dart';

class CreerRapportScreen extends StatefulWidget {
  final String? missionId;
  final RapportMerchandising? rapport;

  const CreerRapportScreen({super.key, this.missionId, this.rapport});

  @override
  State<CreerRapportScreen> createState() => _CreerRapportScreenState();
}

class _CreerRapportScreenState extends State<CreerRapportScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titreController = TextEditingController();
  final _observationsController = TextEditingController();
  final _recommandationsController = TextEditingController();

  String? _missionSelectionnee;
  List<File> _photos = [];
  bool _isLoading = false;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _missionSelectionnee = widget.missionId;

    if (widget.rapport != null) {
      _titreController.text = widget.rapport!.titre;
      _observationsController.text = widget.rapport!.observations;
      _recommandationsController.text = widget.rapport!.recommandations ?? '';
      _missionSelectionnee = widget.rapport!.missionId;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerMissions();
    });
  }

  void _chargerMissions() {
    final provider = Provider.of<MissionProvider>(context, listen: false);
    provider.chargerMissions();
  }

  @override
  void dispose() {
    _titreController.dispose();
    _observationsController.dispose();
    _recommandationsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.rapport == null ? 'Nouveau rapport' : 'Modifier le rapport',
        ),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _sauvegarderBrouillon,
            child: const Text(
              'Brouillon',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Sélection de mission
              Consumer<MissionProvider>(
                builder: (context, provider, child) {
                  return DropdownButtonFormField<String>(
                    value: _missionSelectionnee,
                    decoration: const InputDecoration(
                      labelText: 'Mission',
                      border: OutlineInputBorder(),
                    ),
                    items:
                        provider.missions.map((mission) {
                          return DropdownMenuItem(
                            value: mission.id,
                            child: Text(
                              '${mission.titre} - ${mission.magasinNom}',
                            ),
                          );
                        }).toList(),
                    onChanged:
                        widget.missionId == null
                            ? (value) {
                              setState(() {
                                _missionSelectionnee = value;
                              });
                            }
                            : null,
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Veuillez sélectionner une mission';
                      }
                      return null;
                    },
                  );
                },
              ),

              const SizedBox(height: 16),

              // Titre
              TextFormField(
                controller: _titreController,
                decoration: const InputDecoration(
                  labelText: 'Titre du rapport',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Veuillez saisir un titre';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Observations
              TextFormField(
                controller: _observationsController,
                maxLines: 4,
                decoration: const InputDecoration(
                  labelText: 'Observations',
                  hintText: 'Décrivez ce que vous avez observé...',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Veuillez saisir vos observations';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // Recommandations
              TextFormField(
                controller: _recommandationsController,
                maxLines: 3,
                decoration: const InputDecoration(
                  labelText: 'Recommandations (optionnel)',
                  hintText: 'Vos recommandations pour améliorer...',
                  border: OutlineInputBorder(),
                ),
              ),

              const SizedBox(height: 24),

              // Photos
              const Text(
                'Photos',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),

              // Boutons pour ajouter des photos
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () => _ajouterPhoto(ImageSource.camera),
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Appareil photo'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton.icon(
                    onPressed: () => _ajouterPhoto(ImageSource.gallery),
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Galerie'),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Grille des photos
              if (_photos.isNotEmpty) ...[
                GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 8,
                  ),
                  itemCount: _photos.length,
                  itemBuilder: (context, index) {
                    return Stack(
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(
                              image: FileImage(_photos[index]),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        Positioned(
                          top: 4,
                          right: 4,
                          child: GestureDetector(
                            onTap: () => _supprimerPhoto(index),
                            child: Container(
                              padding: const EdgeInsets.all(4),
                              decoration: const BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 16,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 24),
              ],

              // Boutons d'action
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('Annuler'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _envoyerRapport,
                      child:
                          _isLoading
                              ? const CircularProgressIndicator()
                              : const Text('Envoyer'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _ajouterPhoto(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(source: source);
      if (image != null) {
        setState(() {
          _photos.add(File(image.path));
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Erreur lors de l\'ajout de la photo: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _supprimerPhoto(int index) {
    setState(() {
      _photos.removeAt(index);
    });
  }

  Future<void> _sauvegarderBrouillon() async {
    await _sauvegarder(statut: 'brouillon');
  }

  Future<void> _envoyerRapport() async {
    if (!_formKey.currentState!.validate()) return;
    await _sauvegarder(statut: 'envoye');
  }

  Future<void> _sauvegarder({required String statut}) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final provider = Provider.of<RapportProvider>(context, listen: false);

      final rapport = RapportMerchandising(
        id: widget.rapport?.id,
        missionId: _missionSelectionnee!,
        titre: _titreController.text.trim(),
        observations: _observationsController.text.trim(),
        recommandations:
            _recommandationsController.text.trim().isEmpty
                ? null
                : _recommandationsController.text.trim(),
        dateCreation: widget.rapport?.dateCreation ?? DateTime.now(),
        dateModification: DateTime.now(),
        statut: statut,
        photos: _photos.map((f) => f.path).toList(),
      );

      bool success;
      if (widget.rapport == null) {
        success = await provider.creerRapportMerchandising(rapport);
      } else {
        success = await provider.modifierRapportMerchandising(rapport);
      }

      if (success && mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              statut == 'brouillon'
                  ? 'Rapport sauvegardé en brouillon'
                  : 'Rapport envoyé avec succès',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
