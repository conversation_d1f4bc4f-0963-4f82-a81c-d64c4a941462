import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/mission.dart';
import '../../models/tache_merchandising.dart';
import '../../providers/tache_merchandising_provider.dart';
import 'ajouter_tache_screen.dart';
import 'detail_tache_screen.dart';
import 'creer_rapport_screen.dart';

class DetailMissionScreen extends StatefulWidget {
  final Mission mission;

  const DetailMissionScreen({
    super.key,
    required this.mission,
  });

  @override
  State<DetailMissionScreen> createState() => _DetailMissionScreenState();
}

class _DetailMissionScreenState extends State<DetailMissionScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerTaches();
    });
  }

  void _chargerTaches() {
    final provider = Provider.of<TacheMerchandisingProvider>(context, listen: false);
    provider.chargerTachesPourMission(widget.mission.id);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.mission.titre),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add_task),
            onPressed: _ajouterTache,
          ),
          IconButton(
            icon: const Icon(Icons.description),
            onPressed: _creerRapport,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Informations de la mission
            _buildMissionInfo(),
            
            const SizedBox(height: 24),
            
            // Tâches
            _buildTachesSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildMissionInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Informations de la mission',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow('Titre', widget.mission.titre),
            _buildInfoRow('Description', widget.mission.description),
            _buildInfoRow('Magasin', widget.mission.magasinNom),
            _buildInfoRow('Priorité', widget.mission.priorite.toUpperCase()),
            _buildInfoRow(
              'Date d\'échéance', 
              '${widget.mission.dateEcheance.day}/${widget.mission.dateEcheance.month}/${widget.mission.dateEcheance.year}'
            ),
            _buildInfoRow(
              'Statut', 
              _getStatutText(widget.mission.statut),
            ),
            
            if (widget.mission.notes != null) ...[
              const SizedBox(height: 8),
              const Text(
                'Notes:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(widget.mission.notes!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildTachesSection() {
    return Consumer<TacheMerchandisingProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Tâches',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _ajouterTache,
                  icon: const Icon(Icons.add),
                  label: const Text('Ajouter'),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            if (provider.isLoading)
              const Center(child: CircularProgressIndicator())
            else if (provider.taches.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('Aucune tâche pour cette mission'),
                ),
              )
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: provider.taches.length,
                itemBuilder: (context, index) {
                  final tache = provider.taches[index];
                  return _buildTacheCard(tache);
                },
              ),
          ],
        );
      },
    );
  }

  Widget _buildTacheCard(TacheMerchandising tache) {
    Color prioriteColor;
    switch (tache.priorite) {
      case 'urgente':
        prioriteColor = Colors.red;
        break;
      case 'haute':
        prioriteColor = Colors.orange;
        break;
      case 'normale':
        prioriteColor = Colors.blue;
        break;
      case 'faible':
        prioriteColor = Colors.green;
        break;
      default:
        prioriteColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          width: 4,
          height: double.infinity,
          color: prioriteColor,
        ),
        title: Text(
          tache.titre,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            decoration: tache.statut == 'terminee' 
                ? TextDecoration.lineThrough 
                : null,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (tache.description != null) ...[
              Text(tache.description!),
              const SizedBox(height: 4),
            ],
            Row(
              children: [
                Icon(
                  Icons.flag,
                  size: 16,
                  color: prioriteColor,
                ),
                const SizedBox(width: 4),
                Text(
                  tache.priorite.toUpperCase(),
                  style: TextStyle(
                    color: prioriteColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                if (tache.dateEcheance != null) ...[
                  Icon(
                    Icons.schedule,
                    size: 16,
                    color: Colors.grey[600],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${tache.dateEcheance!.day}/${tache.dateEcheance!.month}',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: _getStatutColor(tache.statut).withOpacity(0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            _getStatutText(tache.statut),
            style: TextStyle(
              color: _getStatutColor(tache.statut),
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DetailTacheScreen(tache: tache),
            ),
          ).then((_) => _chargerTaches());
        },
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'terminee':
      case 'termine':
        return Colors.green;
      case 'en_cours':
        return Colors.orange;
      case 'en_attente':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatutText(String statut) {
    switch (statut) {
      case 'terminee':
      case 'termine':
        return 'Terminée';
      case 'en_cours':
        return 'En cours';
      case 'en_attente':
        return 'En attente';
      default:
        return 'Inconnu';
    }
  }

  void _ajouterTache() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AjouterTacheScreen(missionId: widget.mission.id),
      ),
    ).then((result) {
      if (result == true) {
        _chargerTaches();
      }
    });
  }

  void _creerRapport() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreerRapportScreen(missionId: widget.mission.id),
      ),
    );
  }
}
