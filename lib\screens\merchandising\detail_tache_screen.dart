import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/tache_merchandising.dart';
import '../../providers/tache_merchandising_provider.dart';
import 'ajouter_tache_screen.dart';

class DetailTacheScreen extends StatefulWidget {
  final TacheMerchandising tache;

  const DetailTacheScreen({
    super.key,
    required this.tache,
  });

  @override
  State<DetailTacheScreen> createState() => _DetailTacheScreenState();
}

class _DetailTacheScreenState extends State<DetailTacheScreen> {
  late TacheMerchandising _tache;

  @override
  void initState() {
    super.initState();
    _tache = widget.tache;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_tache.titre),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: _modifierTache,
          ),
          PopupMenuButton<String>(
            onSelected: _onMenuSelected,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'marquer_terminee',
                child: Text('Marquer comme terminée'),
              ),
              const PopupMenuItem(
                value: 'marquer_en_cours',
                child: Text('Marquer en cours'),
              ),
              const PopupMenuItem(
                value: 'supprimer',
                child: Text('Supprimer'),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Statut et priorité
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatutColor(_tache.statut).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _getStatutText(_tache.statut),
                    style: TextStyle(
                      color: _getStatutColor(_tache.statut),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getPrioriteColor(_tache.priorite).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.flag,
                        size: 16,
                        color: _getPrioriteColor(_tache.priorite),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        _tache.priorite.toUpperCase(),
                        style: TextStyle(
                          color: _getPrioriteColor(_tache.priorite),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Informations principales
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Informations',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    _buildInfoRow('Titre', _tache.titre),
                    
                    if (_tache.description != null && _tache.description!.isNotEmpty)
                      _buildInfoRow('Description', _tache.description!),
                    
                    _buildInfoRow('Priorité', _tache.priorite.toUpperCase()),
                    _buildInfoRow('Statut', _getStatutText(_tache.statut)),
                    
                    _buildInfoRow(
                      'Date de création', 
                      '${_tache.dateCreation.day}/${_tache.dateCreation.month}/${_tache.dateCreation.year}'
                    ),
                    
                    if (_tache.dateEcheance != null)
                      _buildInfoRow(
                        'Date d\'échéance', 
                        '${_tache.dateEcheance!.day}/${_tache.dateEcheance!.month}/${_tache.dateEcheance!.year}'
                      ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Actions rapides
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Actions',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    if (_tache.statut != 'terminee') ...[
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => _changerStatut('terminee'),
                          icon: const Icon(Icons.check_circle),
                          label: const Text('Marquer comme terminée'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                    
                    if (_tache.statut != 'en_cours') ...[
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => _changerStatut('en_cours'),
                          icon: const Icon(Icons.play_circle),
                          label: const Text('Marquer en cours'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                    
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _modifierTache,
                        icon: const Icon(Icons.edit),
                        label: const Text('Modifier'),
                      ),
                    ),
                    
                    const SizedBox(height: 8),
                    
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _confirmerSuppression,
                        icon: const Icon(Icons.delete),
                        label: const Text('Supprimer'),
                        style: OutlinedButton.styleFrom(
                          foregroundColor: Colors.red,
                          side: const BorderSide(color: Colors.red),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'terminee':
        return Colors.green;
      case 'en_cours':
        return Colors.orange;
      case 'en_attente':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _getStatutText(String statut) {
    switch (statut) {
      case 'terminee':
        return 'Terminée';
      case 'en_cours':
        return 'En cours';
      case 'en_attente':
        return 'En attente';
      default:
        return 'Inconnu';
    }
  }

  Color _getPrioriteColor(String priorite) {
    switch (priorite) {
      case 'urgente':
        return Colors.red;
      case 'haute':
        return Colors.orange;
      case 'normale':
        return Colors.blue;
      case 'faible':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  void _onMenuSelected(String value) {
    switch (value) {
      case 'marquer_terminee':
        _changerStatut('terminee');
        break;
      case 'marquer_en_cours':
        _changerStatut('en_cours');
        break;
      case 'supprimer':
        _confirmerSuppression();
        break;
    }
  }

  Future<void> _changerStatut(String nouveauStatut) async {
    try {
      final provider = Provider.of<TacheMerchandisingProvider>(context, listen: false);
      
      final tacheModifiee = TacheMerchandising(
        id: _tache.id,
        missionId: _tache.missionId,
        titre: _tache.titre,
        description: _tache.description,
        priorite: _tache.priorite,
        dateCreation: _tache.dateCreation,
        dateEcheance: _tache.dateEcheance,
        statut: nouveauStatut,
      );

      final success = await provider.modifierTache(tacheModifiee);
      
      if (success && mounted) {
        setState(() {
          _tache = tacheModifiee;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Statut mis à jour: ${_getStatutText(nouveauStatut)}'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _modifierTache() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AjouterTacheScreen(
          missionId: _tache.missionId,
          tache: _tache,
        ),
      ),
    ).then((result) {
      if (result == true) {
        // Recharger les données de la tâche
        Navigator.pop(context, true);
      }
    });
  }

  void _confirmerSuppression() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: const Text('Êtes-vous sûr de vouloir supprimer cette tâche ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _supprimerTache();
            },
            child: const Text('Supprimer'),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
          ),
        ],
      ),
    );
  }

  Future<void> _supprimerTache() async {
    try {
      final provider = Provider.of<TacheMerchandisingProvider>(context, listen: false);
      final success = await provider.supprimerTache(_tache.id!);
      
      if (success && mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Tâche supprimée avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
