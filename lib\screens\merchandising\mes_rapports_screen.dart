import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/rapport_merchandising.dart';
import '../../providers/rapport_provider.dart';
import 'creer_rapport_screen.dart';

class MesRapportsScreen extends StatefulWidget {
  const MesRapportsScreen({super.key});

  @override
  State<MesRapportsScreen> createState() => _MesRapportsScreenState();
}

class _MesRapportsScreenState extends State<MesRapportsScreen> {
  String _filtreStatut = 'tous';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _chargerRapports();
    });
  }

  void _chargerRapports() {
    final provider = Provider.of<RapportProvider>(context, listen: false);
    provider.chargerRapports();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Mes rapports'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _chargerRapports,
          ),
        ],
      ),
      body: Column(
        children: [
          // Filtres
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                const Text('Filtrer par statut: '),
                const SizedBox(width: 8),
                Expanded(
                  child: DropdownButton<String>(
                    value: _filtreStatut,
                    isExpanded: true,
                    items: const [
                      DropdownMenuItem(value: 'tous', child: Text('Tous')),
                      DropdownMenuItem(
                        value: 'brouillon',
                        child: Text('Brouillons'),
                      ),
                      DropdownMenuItem(value: 'envoye', child: Text('Envoyés')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _filtreStatut = value!;
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          // Liste des rapports
          Expanded(
            child: Consumer<RapportProvider>(
              builder: (context, provider, child) {
                if (provider.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final rapportsFiltres = _filtrerRapports(
                  provider.rapportsMerchandising,
                );

                if (rapportsFiltres.isEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.description_outlined,
                          size: 64,
                          color: Colors.grey[400],
                        ),
                        const SizedBox(height: 16),
                        Text(
                          _filtreStatut == 'tous'
                              ? 'Aucun rapport trouvé'
                              : 'Aucun rapport ${_filtreStatut == "brouillon" ? "en brouillon" : "envoyé"}',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => const CreerRapportScreen(),
                              ),
                            ).then((_) => _chargerRapports());
                          },
                          icon: const Icon(Icons.add),
                          label: const Text('Créer un rapport'),
                        ),
                      ],
                    ),
                  );
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: rapportsFiltres.length,
                  itemBuilder: (context, index) {
                    final rapport = rapportsFiltres[index];
                    return _buildRapportCard(rapport);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const CreerRapportScreen()),
          ).then((_) => _chargerRapports());
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  List<RapportMerchandising> _filtrerRapports(
    List<RapportMerchandising> rapports,
  ) {
    if (_filtreStatut == 'tous') {
      return rapports;
    }
    return rapports
        .where((rapport) => rapport.statut == _filtreStatut)
        .toList();
  }

  Widget _buildRapportCard(RapportMerchandising rapport) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getStatutColor(rapport.statut).withOpacity(0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            rapport.statut == 'brouillon' ? Icons.edit : Icons.send,
            color: _getStatutColor(rapport.statut),
          ),
        ),
        title: Text(
          rapport.titre,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              rapport.observations.length > 100
                  ? '${rapport.observations.substring(0, 100)}...'
                  : rapport.observations,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: _getStatutColor(rapport.statut).withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    _getStatutText(rapport.statut),
                    style: TextStyle(
                      color: _getStatutColor(rapport.statut),
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Spacer(),
                Text(
                  '${rapport.dateCreation.day}/${rapport.dateCreation.month}/${rapport.dateCreation.year}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) => _onMenuSelected(value, rapport),
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'voir',
                  child: Text('Voir les détails'),
                ),
                if (rapport.statut == 'brouillon') ...[
                  const PopupMenuItem(
                    value: 'modifier',
                    child: Text('Modifier'),
                  ),
                  const PopupMenuItem(value: 'envoyer', child: Text('Envoyer')),
                ],
                const PopupMenuItem(
                  value: 'supprimer',
                  child: Text('Supprimer'),
                ),
              ],
        ),
        onTap: () => _voirDetails(rapport),
      ),
    );
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'envoye':
        return Colors.green;
      case 'brouillon':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getStatutText(String statut) {
    switch (statut) {
      case 'envoye':
        return 'Envoyé';
      case 'brouillon':
        return 'Brouillon';
      default:
        return 'Inconnu';
    }
  }

  void _onMenuSelected(String value, RapportMerchandising rapport) {
    switch (value) {
      case 'voir':
        _voirDetails(rapport);
        break;
      case 'modifier':
        _modifierRapport(rapport);
        break;
      case 'envoyer':
        _envoyerRapport(rapport);
        break;
      case 'supprimer':
        _confirmerSuppression(rapport);
        break;
    }
  }

  void _voirDetails(RapportMerchandising rapport) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(rapport.titre),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Statut: ${_getStatutText(rapport.statut)}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Date: ${rapport.dateCreation.day}/${rapport.dateCreation.month}/${rapport.dateCreation.year}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Observations:',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 4),
                  Text(rapport.observations),
                  if (rapport.recommandations != null) ...[
                    const SizedBox(height: 16),
                    const Text(
                      'Recommandations:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(rapport.recommandations!),
                  ],
                  if (rapport.photos.isNotEmpty) ...[
                    const SizedBox(height: 16),
                    Text(
                      'Photos: ${rapport.photos.length}',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Fermer'),
              ),
              if (rapport.statut == 'brouillon')
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _modifierRapport(rapport);
                  },
                  child: const Text('Modifier'),
                ),
            ],
          ),
    );
  }

  void _modifierRapport(RapportMerchandising rapport) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreerRapportScreen(rapport: rapport),
      ),
    ).then((_) => _chargerRapports());
  }

  Future<void> _envoyerRapport(RapportMerchandising rapport) async {
    try {
      final provider = Provider.of<RapportProvider>(context, listen: false);

      final rapportModifie = RapportMerchandising(
        id: rapport.id,
        missionId: rapport.missionId,
        titre: rapport.titre,
        observations: rapport.observations,
        recommandations: rapport.recommandations,
        dateCreation: rapport.dateCreation,
        dateModification: DateTime.now(),
        statut: 'envoye',
        photos: rapport.photos,
      );

      final success = await provider.modifierRapportMerchandising(
        rapportModifie,
      );

      if (success && mounted) {
        _chargerRapports();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Rapport envoyé avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _confirmerSuppression(RapportMerchandising rapport) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Confirmer la suppression'),
            content: Text(
              'Êtes-vous sûr de vouloir supprimer le rapport "${rapport.titre}" ?',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('Annuler'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  _supprimerRapport(rapport);
                },
                child: const Text('Supprimer'),
                style: TextButton.styleFrom(foregroundColor: Colors.red),
              ),
            ],
          ),
    );
  }

  Future<void> _supprimerRapport(RapportMerchandising rapport) async {
    try {
      final provider = Provider.of<RapportProvider>(context, listen: false);
      final success = await provider.supprimerRapportMerchandising(rapport.id!);

      if (success && mounted) {
        _chargerRapports();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Rapport supprimé avec succès')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Erreur: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }
}
