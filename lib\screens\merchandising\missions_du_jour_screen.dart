import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/mission_provider.dart';

import '../../models/mission.dart';
import 'detail_mission_screen.dart';

class MissionsDuJourScreen extends StatefulWidget {
  @override
  _MissionsDuJourScreenState createState() => _MissionsDuJourScreenState();
}

class _MissionsDuJourScreenState extends State<MissionsDuJourScreen> {
  String _filtreStatut = 'tous';

  @override
  void initState() {
    super.initState();
    _chargerMissions();
  }

  void _chargerMissions() {
    final missionProvider = Provider.of<MissionProvider>(
      context,
      listen: false,
    );

    // Simuler un ID de merchandiser - vous devrez adapter cela selon votre AuthProvider
    final merchandiserId = 'merchandiser_123';

    missionProvider.chargerMissionsDuJour(merchandiserId);
    missionProvider.chargerMissionsEnRetard(merchandiserId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Missions du jour'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<MissionProvider>(
        builder: (context, missionProvider, child) {
          if (missionProvider.isLoading) {
            return Center(child: CircularProgressIndicator());
          }

          if (missionProvider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error, size: 64, color: Colors.red),
                  SizedBox(height: 16),
                  Text(
                    'Erreur: ${missionProvider.error}',
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _chargerMissions,
                    child: Text('Réessayer'),
                  ),
                ],
              ),
            );
          }

          final missionsDuJour = missionProvider.missionsDuJour;
          final missionsEnRetard = missionProvider.missionsEnRetard;
          final toutesLesMissions = [...missionsEnRetard, ...missionsDuJour];

          // Filtrer les missions selon le statut sélectionné
          final missionsFiltrees =
              _filtreStatut == 'tous'
                  ? toutesLesMissions
                  : toutesLesMissions
                      .where((m) => m.statut == _filtreStatut)
                      .toList();

          return RefreshIndicator(
            onRefresh: () async {
              _chargerMissions();
            },
            child: Column(
              children: [
                // Filtres
                Container(
                  padding: EdgeInsets.all(16),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        _buildFilterChip(
                          'Tous',
                          'tous',
                          missionsFiltrees.length,
                        ),
                        SizedBox(width: 8),
                        _buildFilterChip(
                          'En attente',
                          'en_attente',
                          toutesLesMissions
                              .where((m) => m.statut == 'en_attente')
                              .length,
                        ),
                        SizedBox(width: 8),
                        _buildFilterChip(
                          'En cours',
                          'en_cours',
                          toutesLesMissions
                              .where((m) => m.statut == 'en_cours')
                              .length,
                        ),
                        SizedBox(width: 8),
                        _buildFilterChip(
                          'Terminées',
                          'terminee',
                          toutesLesMissions
                              .where((m) => m.statut == 'terminee')
                              .length,
                        ),
                      ],
                    ),
                  ),
                ),

                Expanded(
                  child:
                      missionsFiltrees.isEmpty
                          ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.assignment_turned_in,
                                  size: 64,
                                  color: Colors.grey,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Aucune mission pour aujourd\'hui',
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: Colors.grey,
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Profitez de cette journée calme!',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          )
                          : ListView.builder(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            itemCount: missionsFiltrees.length,
                            itemBuilder: (context, index) {
                              final mission = missionsFiltrees[index];
                              final isEnRetard = missionsEnRetard.contains(
                                mission,
                              );

                              return _buildMissionCard(mission, isEnRetard);
                            },
                          ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFilterChip(String label, String value, int count) {
    final isSelected = _filtreStatut == value;
    return FilterChip(
      label: Text('$label ($count)'),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _filtreStatut = value;
        });
      },
      selectedColor: Colors.blue.shade100,
      checkmarkColor: Colors.blue,
    );
  }

  Widget _buildMissionCard(Mission mission, bool isEnRetard) {
    return Container(
      margin: EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: isEnRetard ? Border.all(color: Colors.red, width: 2) : null,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => DetailMissionScreen(mission: mission),
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      mission.titre,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        decoration:
                            mission.estTerminee
                                ? TextDecoration.lineThrough
                                : null,
                      ),
                    ),
                  ),
                  if (isEnRetard)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'EN RETARD',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                ],
              ),

              SizedBox(height: 8),

              Row(
                children: [
                  Icon(Icons.store, size: 16, color: Colors.grey),
                  SizedBox(width: 4),
                  Text(
                    mission.magasinNom,
                    style: TextStyle(color: Colors.grey.shade600, fontSize: 14),
                  ),
                ],
              ),

              SizedBox(height: 4),

              Row(
                children: [
                  Icon(Icons.schedule, size: 16, color: Colors.grey),
                  SizedBox(width: 4),
                  Text(
                    'Échéance: ${mission.dateEcheance.day}/${mission.dateEcheance.month}/${mission.dateEcheance.year}',
                    style: TextStyle(
                      color: isEnRetard ? Colors.red : Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),

              SizedBox(height: 8),

              Text(
                mission.description,
                style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              SizedBox(height: 12),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getPrioriteIcon(mission.priorite),
                        size: 16,
                        color: _getPrioriteColor(mission.priorite),
                      ),
                      SizedBox(width: 4),
                      Text(
                        mission.prioriteAffichage,
                        style: TextStyle(
                          fontSize: 12,
                          color: _getPrioriteColor(mission.priorite),
                        ),
                      ),
                    ],
                  ),
                  Chip(
                    label: Text(
                      mission.statutAffichage,
                      style: TextStyle(fontSize: 12),
                    ),
                    backgroundColor: _getStatutColor(mission.statut),
                  ),
                ],
              ),

              if (mission.taches.isNotEmpty) ...[
                SizedBox(height: 8),
                Text(
                  'Tâches: ${mission.taches.length}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  IconData _getPrioriteIcon(String priorite) {
    switch (priorite) {
      case 'faible':
        return Icons.arrow_downward;
      case 'normale':
        return Icons.remove;
      case 'haute':
        return Icons.arrow_upward;
      case 'urgente':
        return Icons.priority_high;
      default:
        return Icons.remove;
    }
  }

  Color _getPrioriteColor(String priorite) {
    switch (priorite) {
      case 'faible':
        return Colors.green;
      case 'normale':
        return Colors.blue;
      case 'haute':
        return Colors.orange;
      case 'urgente':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  Color _getStatutColor(String statut) {
    switch (statut) {
      case 'en_attente':
        return Colors.orange.shade100;
      case 'en_cours':
        return Colors.blue.shade100;
      case 'terminee':
        return Colors.green.shade100;
      case 'annulee':
        return Colors.red.shade100;
      default:
        return Colors.grey.shade100;
    }
  }
}
