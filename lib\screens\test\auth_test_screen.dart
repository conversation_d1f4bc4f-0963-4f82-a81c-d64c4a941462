import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../providers/auth_provider.dart';
import '../../services/firebase_service.dart';

class AuthTestScreen extends StatefulWidget {
  const AuthTestScreen({super.key});

  @override
  State<AuthTestScreen> createState() => _AuthTestScreenState();
}

class _AuthTestScreenState extends State<AuthTestScreen> {
  String _testStatus = 'Prêt à tester';
  bool _isLoading = false;

  Future<void> _testAuthFlow() async {
    setState(() {
      _isLoading = true;
      _testStatus = 'Test en cours...';
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      // Test 1: Inscription d'un utilisateur commercial
      print('🔥 Test 1: Inscription utilisateur commercial...');
      final testEmail =
          'test-commercial-${DateTime.now().millisecondsSinceEpoch}@example.com';

      final signupSuccess = await authProvider.signup(
        nomComplet: 'Test Commercial User',
        email: testEmail,
        password: 'password123',
        telephone: '**********',
        mobile: '**********',
        territoire: 'Test Territory',
        userType: 'commercial',
        status: 'inactif',
      );

      if (signupSuccess) {
        print('✅ Test 1 réussi: Utilisateur commercial créé');

        // Test 2: Vérifier que l'utilisateur apparaît dans la liste des utilisateurs en attente
        print('🔥 Test 2: Vérification liste utilisateurs en attente...');

        // Récupérer les utilisateurs commerciaux en attente
        final commercialDocs =
            await FirebaseService.firestore
                .collection('commercial')
                .where('statut', isEqualTo: 'inactif')
                .get();

        final pendingUsers =
            commercialDocs.docs
                .map(
                  (doc) => {
                    'uid': doc.id,
                    'userType': 'commercial',
                    ...doc.data(),
                  },
                )
                .toList();

        final foundUser = pendingUsers.any(
          (user) => user['email'] == testEmail,
        );

        if (foundUser) {
          print('✅ Test 2 réussi: Utilisateur trouvé dans la liste en attente');

          // Test 3: Tenter de se connecter (devrait échouer car inactif)
          print('🔥 Test 3: Tentative de connexion avec compte inactif...');
          final loginFailed = await authProvider.login(
            testEmail,
            'password123',
          );

          if (!loginFailed) {
            print('✅ Test 3 réussi: Connexion refusée pour compte inactif');

            setState(() {
              _testStatus =
                  '✅ Tests réussis!\n\n'
                  '• Inscription ✓\n'
                  '• Statut inactif par défaut ✓\n'
                  '• Connexion bloquée pour compte inactif ✓\n\n'
                  'Le système d\'authentification fonctionne correctement!';
              _isLoading = false;
            });
          } else {
            throw Exception(
              'Le test a échoué: la connexion aurait dû être refusée',
            );
          }
        } else {
          throw Exception('Utilisateur non trouvé dans la liste en attente');
        }
      } else {
        throw Exception('Échec de l\'inscription: ${authProvider.error}');
      }
    } catch (e) {
      print('❌ Erreur lors du test: $e');
      setState(() {
        _testStatus = '❌ Erreur: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Système d\'Authentification'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _testStatus.startsWith('✅')
                  ? Icons.check_circle
                  : _testStatus.startsWith('❌')
                  ? Icons.error
                  : Icons.info,
              size: 64,
              color:
                  _testStatus.startsWith('✅')
                      ? Colors.green
                      : _testStatus.startsWith('❌')
                      ? Colors.red
                      : Colors.blue,
            ),
            const SizedBox(height: 20),
            Text(
              _testStatus,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: _isLoading ? null : _testAuthFlow,
              child:
                  _isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text('Tester le Système d\'Auth'),
            ),
            const SizedBox(height: 20),
            Text(
              'Ce test vérifie:\n'
              '• L\'inscription fonctionne\n'
              '• Le statut par défaut est "inactif"\n'
              '• La connexion est bloquée pour les comptes inactifs',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
