import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';
import 'providers/firebase_auth_provider.dart';

/// Test pour vérifier que la correction du bypass de statut fonctionne
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Initialiser Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialisé avec succès');

    // Initialiser Firestore
    await FirebaseService.initializeFirestore();
    print('✅ Firestore initialisé avec succès');

    // Créer une instance du provider
    final authProvider = FirebaseAuthProvider();
    print('✅ FirebaseAuthProvider créé');

    // Test avec un utilisateur inactif
    print('\n🔄 Test de connexion avec un utilisateur INACTIF...');
    print('📧 Email de test: <EMAIL>');
    print('🔑 Mot de passe: password123');
    print('📊 Statut attendu dans la base: inactif');
    
    final loginResult = await authProvider.signInWithEmailAndPassword(
      '<EMAIL>',
      'password123',
    );

    if (loginResult) {
      print('❌ ERREUR: La connexion a réussi alors que l\'utilisateur est inactif!');
      print('🚨 Le bypass de statut n\'a pas été corrigé correctement');
      
      // Vérifier le profil pour confirmer le statut
      final profile = await authProvider.getUserProfile();
      print('📄 Profil utilisateur: $profile');
      print('📊 Statut dans le profil: ${profile?['status']}');
      
    } else {
      print('✅ SUCCÈS: La connexion a été refusée pour l\'utilisateur inactif');
      print('🔒 Message d\'erreur: ${authProvider.errorMessage}');
      print('✅ La correction du bypass de statut fonctionne correctement!');
    }

    print('\n🔄 Test de connexion avec un utilisateur ACTIF...');
    print('📧 Email de test: <EMAIL>');
    print('🔑 Mot de passe: password123');
    print('📊 Statut attendu dans la base: actif');
    
    final loginResult2 = await authProvider.signInWithEmailAndPassword(
      '<EMAIL>',
      'password123',
    );

    if (loginResult2) {
      print('✅ SUCCÈS: La connexion a réussi pour l\'utilisateur actif');
      
      // Vérifier le profil pour confirmer le statut
      final profile = await authProvider.getUserProfile();
      print('📄 Profil utilisateur: $profile');
      print('📊 Statut dans le profil: ${profile?['status']}');
      
      // Se déconnecter après le test
      await authProvider.signOut();
      print('🔓 Déconnexion effectuée');
      
    } else {
      print('❌ ERREUR: La connexion a échoué pour l\'utilisateur actif');
      print('🔒 Message d\'erreur: ${authProvider.errorMessage}');
    }
    
    print('\n✅ Tests de vérification du statut terminés!');
    print('📋 Résumé:');
    print('   - Utilisateur inactif: ${loginResult ? "❌ BYPASS DÉTECTÉ" : "✅ BLOQUÉ CORRECTEMENT"}');
    print('   - Utilisateur actif: ${loginResult2 ? "✅ AUTORISÉ CORRECTEMENT" : "❌ BLOQUÉ À TORT"}');
    
  } catch (e) {
    print('❌ Erreur lors du test: $e');
    print('📍 Stack trace: ${StackTrace.current}');
  }
}
