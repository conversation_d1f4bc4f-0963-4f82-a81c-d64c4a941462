import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';
import 'models/client.dart';

/// Test pour vérifier que les champs du client correspondent à votre structure de données
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  try {
    // Initialiser Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialisé avec succès');

    // Initialiser Firestore
    await FirebaseService.initializeFirestore();
    print('✅ Firestore initialisé avec succès');

    // Tester avec vos données réelles
    print('\n🔍 Test de création d\'un client avec vos champs...');
    
    final testClient = Client(
      adresse: "zuhuzerf",
      codeClient: "fdgqdfg",
      company: "tzqt",
      createdAt: DateTime.parse("2025-07-20T13:24:35.000Z"),
      email: "<EMAIL>",
      fax: "5896321899",
      matriculeFiscale: "558555zerazertaert",
      modeReglement: "especes",
      nomClient: "gsdfghdfh",
      portable: "*********",
      status: "active",
      tel: "25147369",
      updatedAt: DateTime.parse("2025-07-20T13:24:35.000Z"),
    );

    print('✅ Client créé avec succès');
    print('📄 Nom complet: ${testClient.nomComplet}');
    print('📞 Téléphone principal: ${testClient.primaryPhone}');
    print('📅 Date de création: ${testClient.primaryCreationDate}');
    print('🏢 Entreprise: ${testClient.company}');
    print('📊 Statut: ${testClient.status}');
    print('📧 Email: ${testClient.email}');

    // Tester la conversion vers Firebase
    print('\n🔄 Test de conversion vers Firebase...');
    final firestoreData = testClient.toFirestore();
    print('📄 Données Firestore: $firestoreData');

    // Tester la création depuis Firebase
    print('\n🔄 Test de création depuis Firebase...');
    final clientFromFirestore = Client.fromFirestore('test-id', firestoreData);
    print('✅ Client recréé depuis Firestore');
    print('📄 Nom complet: ${clientFromFirestore.nomComplet}');
    print('📞 Téléphone principal: ${clientFromFirestore.primaryPhone}');
    print('📊 Statut: ${clientFromFirestore.status}');

    // Vérifier que tous les champs sont préservés
    print('\n🔍 Vérification des champs...');
    final fieldsToCheck = {
      'adresse': testClient.adresse == clientFromFirestore.adresse,
      'codeClient': testClient.codeClient == clientFromFirestore.codeClient,
      'company': testClient.company == clientFromFirestore.company,
      'email': testClient.email == clientFromFirestore.email,
      'fax': testClient.fax == clientFromFirestore.fax,
      'matriculeFiscale': testClient.matriculeFiscale == clientFromFirestore.matriculeFiscale,
      'modeReglement': testClient.modeReglement == clientFromFirestore.modeReglement,
      'nomClient': testClient.nomClient == clientFromFirestore.nomClient,
      'portable': testClient.portable == clientFromFirestore.portable,
      'status': testClient.status == clientFromFirestore.status,
      'tel': testClient.tel == clientFromFirestore.tel,
    };

    bool allFieldsMatch = true;
    fieldsToCheck.forEach((field, matches) {
      if (matches) {
        print('✅ $field: OK');
      } else {
        print('❌ $field: ERREUR');
        allFieldsMatch = false;
      }
    });

    if (allFieldsMatch) {
      print('\n✅ TOUS LES CHAMPS SONT CORRECTEMENT GÉRÉS!');
      print('🎉 Votre structure de données client est compatible');
    } else {
      print('\n❌ CERTAINS CHAMPS NE SONT PAS CORRECTEMENT GÉRÉS');
    }

    // Test avec un client existant dans Firebase (optionnel)
    print('\n🔍 Test de récupération d\'un client existant...');
    try {
      final snapshot = await FirebaseService.clients.limit(1).get();
      if (snapshot.docs.isNotEmpty) {
        final doc = snapshot.docs.first;
        final existingClient = Client.fromFirestore(doc.id, doc.data() as Map<String, dynamic>);
        print('✅ Client existant récupéré: ${existingClient.nomComplet}');
        print('📞 Téléphone: ${existingClient.primaryPhone}');
        print('📊 Statut: ${existingClient.status}');
        print('🏢 Entreprise: ${existingClient.company}');
      } else {
        print('ℹ️ Aucun client existant trouvé dans la base');
      }
    } catch (e) {
      print('⚠️ Erreur lors de la récupération: $e');
    }
    
    print('\n✅ Tests terminés!');
    
  } catch (e) {
    print('❌ Erreur lors du test: $e');
    print('📍 Stack trace: ${StackTrace.current}');
  }
}
