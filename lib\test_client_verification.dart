import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'models/client.dart';

/// Test pour vérifier que votre structure de données client exacte est respectée
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🔍 Test de vérification de la structure client exacte...');
  
  // Vos données exactes
  final yourExactData = {
    'adresse': "zuhuzerf",
    'codeClient': "fdgqdfg", 
    'company': "tzqt",
    'createdAt': Timestamp.fromDate(DateTime.parse("2025-07-20T13:24:35.000Z")),
    'email': "<EMAIL>",
    'fax': "5896321899",
    'matriculeFiscale': "558555zerazertaert",
    'modeReglement': "especes",
    'nomClient': "gsdfghdfh",
    'portable': "*********",
    'status': "active",
    'tel': "25147369",
    'updatedAt': Timestamp.fromDate(DateTime.parse("2025-07-20T13:24:35.000Z")),
  };

  print('📄 Données d\'entrée:');
  yourExactData.forEach((key, value) {
    print('   $key: $value');
  });

  // Test 1: Créer un client depuis vos données Firebase
  print('\n🔄 Test 1: Création depuis Firebase...');
  final clientFromFirestore = Client.fromFirestore('test-id', yourExactData);
  
  print('✅ Client créé avec succès');
  print('📊 Vérification des champs:');
  
  // Vérifier chaque champ individuellement
  final verifications = {
    'adresse': clientFromFirestore.adresse == "zuhuzerf",
    'codeClient': clientFromFirestore.codeClient == "fdgqdfg",
    'company': clientFromFirestore.company == "tzqt", 
    'email': clientFromFirestore.email == "<EMAIL>",
    'fax': clientFromFirestore.fax == "5896321899",
    'matriculeFiscale': clientFromFirestore.matriculeFiscale == "558555zerazertaert",
    'modeReglement': clientFromFirestore.modeReglement == "especes",
    'nomClient': clientFromFirestore.nomClient == "gsdfghdfh",
    'portable': clientFromFirestore.portable == "*********",
    'status': clientFromFirestore.status == "active",
    'tel': clientFromFirestore.tel == "25147369",
    'createdAt': clientFromFirestore.createdAt != null,
    'updatedAt': clientFromFirestore.updatedAt != null,
  };

  bool allFieldsCorrect = true;
  verifications.forEach((field, isCorrect) {
    final status = isCorrect ? '✅' : '❌';
    print('   $status $field: ${isCorrect ? "OK" : "ERREUR"}');
    if (!isCorrect) allFieldsCorrect = false;
  });

  // Test 2: Conversion vers Firebase
  print('\n🔄 Test 2: Conversion vers Firebase...');
  final firestoreData = clientFromFirestore.toFirestore();
  
  print('📄 Données de sortie:');
  firestoreData.forEach((key, value) {
    print('   $key: $value');
  });

  // Vérifier que toutes vos données sont préservées
  final dataVerifications = {
    'adresse': firestoreData['adresse'] == "zuhuzerf",
    'codeClient': firestoreData['codeClient'] == "fdgqdfg",
    'company': firestoreData['company'] == "tzqt",
    'email': firestoreData['email'] == "<EMAIL>", 
    'fax': firestoreData['fax'] == "5896321899",
    'matriculeFiscale': firestoreData['matriculeFiscale'] == "558555zerazertaert",
    'modeReglement': firestoreData['modeReglement'] == "especes",
    'nomClient': firestoreData['nomClient'] == "gsdfghdfh",
    'portable': firestoreData['portable'] == "*********",
    'status': firestoreData['status'] == "active",
    'tel': firestoreData['tel'] == "25147369",
    'createdAt': firestoreData['createdAt'] is Timestamp,
    'updatedAt': firestoreData['updatedAt'] is Timestamp,
  };

  bool allDataPreserved = true;
  print('\n📊 Vérification de la préservation des données:');
  dataVerifications.forEach((field, isCorrect) {
    final status = isCorrect ? '✅' : '❌';
    print('   $status $field: ${isCorrect ? "PRÉSERVÉ" : "PERDU"}');
    if (!isCorrect) allDataPreserved = false;
  });

  // Test 3: Getters intelligents
  print('\n🔄 Test 3: Getters intelligents...');
  print('   📞 primaryPhone: "${clientFromFirestore.primaryPhone}"');
  print('   👤 nomComplet: "${clientFromFirestore.nomComplet}"');
  print('   📅 primaryCreationDate: ${clientFromFirestore.primaryCreationDate}');
  print('   🏢 matriculeFiscale: "${clientFromFirestore.primaryMatriculeFiscale}"');

  // Résumé final
  print('\n📋 RÉSUMÉ FINAL:');
  if (allFieldsCorrect && allDataPreserved) {
    print('🎉 SUCCÈS: Votre structure de données client est parfaitement respectée!');
    print('✅ Tous les champs sont correctement lus depuis Firebase');
    print('✅ Tous les champs sont correctement sauvegardés vers Firebase');
    print('✅ Les getters intelligents fonctionnent correctement');
  } else {
    print('❌ PROBLÈME: Certains champs ne sont pas correctement gérés');
    if (!allFieldsCorrect) {
      print('   - Erreurs dans la lecture depuis Firebase');
    }
    if (!allDataPreserved) {
      print('   - Erreurs dans la sauvegarde vers Firebase');
    }
  }

  // Test 4: Création manuelle avec vos données
  print('\n🔄 Test 4: Création manuelle...');
  final manualClient = Client(
    adresse: "zuhuzerf",
    codeClient: "fdgqdfg",
    company: "tzqt", 
    createdAt: DateTime.parse("2025-07-20T13:24:35.000Z"),
    email: "<EMAIL>",
    fax: "5896321899",
    matriculeFiscale: "558555zerazertaert",
    modeReglement: "especes",
    nomClient: "gsdfghdfh",
    portable: "*********",
    status: "active",
    tel: "25147369",
    updatedAt: DateTime.parse("2025-07-20T13:24:35.000Z"),
  );

  print('✅ Client créé manuellement avec succès');
  print('   📞 Téléphone principal: "${manualClient.primaryPhone}"');
  print('   👤 Nom complet: "${manualClient.nomComplet}"');
  print('   🏢 Entreprise: "${manualClient.company}"');
  print('   📊 Statut: "${manualClient.status}"');

  print('\n🎯 Test terminé!');
}
