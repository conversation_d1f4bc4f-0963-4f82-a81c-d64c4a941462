import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';

void main() async {
  print('🔧 Début de la correction du statut...');

  try {
    // Initialiser Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialisé avec succès');

    // Initialiser Firestore
    await FirebaseService.initializeFirestore();
    print('✅ Firestore initialisé avec succès');

    // ID de l'utilisateur à corriger (remplacer par l'ID réel)
    const String userId = 'REMPLACER_PAR_ID_UTILISATEUR';

    // Vérifier dans la collection commercials
    final commercialDoc = await FirebaseService.commercials.doc(userId).get();
    if (commercialDoc.exists) {
      final userData = commercialDoc.data();
      print('📄 Données actuelles dans commercials: $userData');

      // Vérifier le statut actuel
      final currentStatus = userData != null ? userData['status'] : null;
      print(
        '📊 Statut actuel: $currentStatus (type: ${currentStatus.runtimeType})',
      );

      // Forcer le statut à 'inactif' (String)
      await FirebaseService.commercials.doc(userId).update({
        'status': 'inactif',
      });

      print('✅ Statut mis à jour à "inactif" dans commercials');

      // Vérifier la mise à jour
      final updatedDoc = await FirebaseService.commercials.doc(userId).get();
      final updatedData = updatedDoc.data();
      print('📄 Données mises à jour: $updatedData');
      final newStatus = updatedData != null ? updatedData['status'] : null;
      print('📊 Nouveau statut: $newStatus');

      return;
    }

    // Vérifier dans la collection merchandizers
    final merchandiserDoc =
        await FirebaseService.merchandizers.doc(userId).get();
    if (merchandiserDoc.exists) {
      final userData = merchandiserDoc.data();
      print('📄 Données actuelles dans merchandizers: $userData');

      // Vérifier le statut actuel
      final currentStatus = userData != null ? userData['status'] : null;
      print(
        '📊 Statut actuel: $currentStatus (type: ${currentStatus.runtimeType})',
      );

      // Forcer le statut à 'inactif' (String)
      await FirebaseService.merchandizers.doc(userId).update({
        'status': 'inactif',
      });

      print('✅ Statut mis à jour à "inactif" dans merchandizers');

      // Vérifier la mise à jour
      final updatedDoc = await FirebaseService.merchandizers.doc(userId).get();
      final updatedData = updatedDoc.data();
      print('📄 Données mises à jour: $updatedData');
      final newStatus = updatedData != null ? updatedData['status'] : null;
      print('📊 Nouveau statut: $newStatus');

      return;
    }

    print('❌ Utilisateur non trouvé dans les collections');
  } catch (e) {
    print('❌ Erreur lors de la correction: $e');
  }
}
