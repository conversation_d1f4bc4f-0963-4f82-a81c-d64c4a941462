import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';
import 'providers/firebase_auth_provider.dart';

void main() async {
  print('🔍 Début du test de vérification de statut...');
  
  try {
    // Initialiser Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialisé avec succès');

    // Initialiser Firestore
    await FirebaseService.initializeFirestore();
    print('✅ Firestore initialisé avec succès');

    // Créer une instance du provider
    final authProvider = FirebaseAuthProvider();
    print('✅ FirebaseAuthProvider créé');

    // Email et mot de passe de test (à remplacer par des valeurs réelles)
    const testEmail = '<EMAIL>';
    const testPassword = 'password123';

    // Tester la connexion
    print('🔄 Test de connexion avec $testEmail...');
    final loginResult = await authProvider.signInWithEmailAndPassword(
      testEmail,
      testPassword,
    );

    if (loginResult) {
      print('✅ Connexion réussie!');
      print('📊 Statut de l\'utilisateur: actif');
      
      // Vérifier le profil utilisateur
      final userProfile = await authProvider.getUserProfile();
      print('📄 Profil utilisateur: $userProfile');
      
      // Afficher explicitement le statut
      final status = userProfile?['status'];
      print('🔍 Statut dans le profil: $status (type: ${status.runtimeType})');
      
      // Déconnexion
      await authProvider.signOut();
      print('✅ Déconnexion réussie');
    } else {
      print('❌ Échec de la connexion: ${authProvider.errorMessage}');
    }

    // Tester directement la méthode de vérification du statut
    print('\n🔍 Test direct de la méthode _checkUserStatus...');
    
    // ID de l'utilisateur à vérifier (à remplacer par un ID réel)
    const userId = 'REMPLACER_PAR_ID_UTILISATEUR';
    
    // Utiliser la réflexion pour accéder à la méthode privée _checkUserStatus
    // Note: Ceci est uniquement pour le test et ne devrait pas être utilisé en production
    final isActive = await authProvider._checkUserStatus(userId);
    print('📊 Résultat de _checkUserStatus: $isActive');
    
    print('\n✅ Tests terminés!');
    
  } catch (e) {
    print('❌ Erreur lors du test: $e');
  }
}