import 'package:firebase_core/firebase_core.dart';
import 'firebase_options.dart';
import 'services/firebase_service.dart';

void main() async {
  print('🔍 Début du test de vérification de statut direct...');
  
  try {
    // Initialiser Firebase
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('✅ Firebase initialisé avec succès');

    // Initialiser Firestore
    await FirebaseService.initializeFirestore();
    print('✅ Firestore initialisé avec succès');

    // ID de l'utilisateur à vérifier (remplacer par un ID réel)
    const String userId = 'REMPLACER_PAR_ID_UTILISATEUR';
    
    // Vérifier directement dans la collection commercials
    final commercialDoc = await FirebaseService.commercials.doc(userId).get();
    if (commercialDoc.exists) {
      final userData = commercialDoc.data();
      print('📄 Données utilisateur dans commercials: $userData');
      
      // Vérifier le statut
      final status = userData?['status'];
      print('📊 Statut actuel: $status (type: ${status.runtimeType})');
      
      // Vérifier si le statut est 'actif'
      final isActive = status == 'actif';
      print('🔍 Le statut est-il "actif"? $isActive');
      
      // Forcer le statut à 'inactif' pour tester
      print('🔄 Changement du statut à "inactif"...');
      await FirebaseService.commercials.doc(userId).update({
        'status': 'inactif'
      });
      
      // Vérifier la mise à jour
      final updatedDoc = await FirebaseService.commercials.doc(userId).get();
      final updatedData = updatedDoc.data();
      final updatedStatus = updatedData?['status'];
      print('📊 Nouveau statut: $updatedStatus');
      
      return;
    }
    
    // Vérifier dans la collection merchandizers
    final merchandiserDoc = await FirebaseService.merchandizers.doc(userId).get();
    if (merchandiserDoc.exists) {
      final userData = merchandiserDoc.data();
      print('📄 Données utilisateur dans merchandizers: $userData');
      
      // Vérifier le statut
      final status = userData?['status'];
      print('📊 Statut actuel: $status (type: ${status.runtimeType})');
      
      // Vérifier si le statut est 'actif'
      final isActive = status == 'actif';
      print('🔍 Le statut est-il "actif"? $isActive');
      
      // Forcer le statut à 'inactif' pour tester
      print('🔄 Changement du statut à "inactif"...');
      await FirebaseService.merchandizers.doc(userId).update({
        'status': 'inactif'
      });
      
      // Vérifier la mise à jour
      final updatedDoc = await FirebaseService.merchandizers.doc(userId).get();
      final updatedData = updatedDoc.data();
      final updatedStatus = updatedData?['status'];
      print('📊 Nouveau statut: $updatedStatus');
      
      return;
    }
    
    print('❌ Utilisateur non trouvé dans les collections');
    
  } catch (e) {
    print('❌ Erreur lors du test: $e');
  }
}