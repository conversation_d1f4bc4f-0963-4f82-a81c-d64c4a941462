import 'package:flutter/material.dart';
import '../models/client.dart';

class WhatsAppMessageDialog extends StatefulWidget {
  final String titre;
  final String messageParDefaut;
  final Client client;
  final VoidCallback onEnvoyer;

  const WhatsAppMessageDialog({
    super.key,
    required this.titre,
    required this.messageParDefaut,
    required this.client,
    required this.onEnvoyer,
  });

  @override
  State<WhatsAppMessageDialog> createState() => _WhatsAppMessageDialogState();
}

class _WhatsAppMessageDialogState extends State<WhatsAppMessageDialog> {
  late TextEditingController _messageController;
  bool _utiliserMessageParDefaut = true;

  @override
  void initState() {
    super.initState();
    _messageController = TextEditingController();
  }

  @override
  void dispose() {
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFF25D366), // Couleur WhatsApp
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.chat, color: Colors.white, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(widget.titre, style: const TextStyle(fontSize: 18)),
          ),
        ],
      ),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Informations client
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.person, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${widget.client.prenom} ${widget.client.nom}',
                          style: const TextStyle(fontWeight: FontWeight.w600),
                        ),
                        if (widget.client.primaryPhone.isNotEmpty)
                          Text(
                            widget.client.primaryPhone,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Option pour personnaliser le message
            CheckboxListTile(
              value: !_utiliserMessageParDefaut,
              onChanged: (value) {
                setState(() {
                  _utiliserMessageParDefaut = !(value ?? false);
                  if (_utiliserMessageParDefaut) {
                    _messageController.clear();
                  }
                });
              },
              title: const Text('Personnaliser le message'),
              contentPadding: EdgeInsets.zero,
              controlAffinity: ListTileControlAffinity.leading,
            ),

            // Zone de texte pour message personnalisé
            if (!_utiliserMessageParDefaut) ...[
              const SizedBox(height: 8),
              TextField(
                controller: _messageController,
                maxLines: 4,
                decoration: const InputDecoration(
                  labelText: 'Message personnalisé',
                  hintText: 'Tapez votre message personnalisé ici...',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Le message personnalisé remplacera seulement l\'introduction. '
                'Les détails du document seront ajoutés automatiquement.',
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],

            const SizedBox(height: 16),

            // Aperçu du message
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.preview, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        'Aperçu',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.grey.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Container(
                    constraints: const BoxConstraints(maxHeight: 120),
                    child: SingleChildScrollView(
                      child: Text(
                        _utiliserMessageParDefaut ||
                                _messageController.text.isEmpty
                            ? widget.messageParDefaut
                            : _messageController.text,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Annuler'),
        ),
        FilledButton.icon(
          onPressed:
              widget.client.primaryPhone.isEmpty
                  ? null
                  : () {
                    // Retourner le message personnalisé ou une chaîne vide si message par défaut
                    final message =
                        _utiliserMessageParDefaut ||
                                _messageController.text.isEmpty
                            ? "" // Chaîne vide pour indiquer l'utilisation du message par défaut
                            : _messageController.text;

                    Navigator.of(context).pop(message);
                  },
          icon: const Icon(Icons.send),
          label: const Text('Envoyer'),
          style: FilledButton.styleFrom(
            backgroundColor: const Color(0xFF25D366),
          ),
        ),
      ],
    );
  }
}

/// Fonction utilitaire pour afficher la boîte de dialogue
Future<String?> showWhatsAppMessageDialog({
  required BuildContext context,
  required String titre,
  required String messageParDefaut,
  required Client client,
}) async {
  return await showDialog<String>(
    context: context,
    builder:
        (context) => WhatsAppMessageDialog(
          titre: titre,
          messageParDefaut: messageParDefaut,
          client: client,
          onEnvoyer: () {},
        ),
  );
}
